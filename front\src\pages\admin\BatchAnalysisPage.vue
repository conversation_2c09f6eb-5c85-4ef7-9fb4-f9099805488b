<template>
  <q-page class="justify-center">
    <q-card>
      <q-card-section>
        <div class="text-h4 text-center q-mb-lg">分析報表</div>

        <!-- 分析方法選擇 -->
        <div class="row q-mb-lg">
          <div class="col-12 col-sm-6">
            <div class="text-h6">分析方法</div>
            <div class="q-pa-sm">
              <q-select
                outlined
                dense
                v-model="selectedMethod"
                :options="analysisMethodOptions"
                map-options
                emit-value
                class="text-h6"
              />
            </div>
          </div>
        </div>

        <!-- 彩種選擇 -->
        <div class="row q-mb-lg">
          <div class="col-12 col-sm-6">
            <div class="text-h6 text-weight-bold">彩種選擇</div>
            <div class="q-pa-sm">
              <q-select
                outlined
                dense
                v-model="selectedLottoType"
                :options="lottoTypeOptions"
                emit-value
                map-options
                @update:model-value="onLottoTypeChange"
              />
            </div>
          </div>
          <div class="col-12 col-sm-6">
            <div class="text-h6 text-weight-bold">參考日期</div>
            <div class="q-pa-sm">
              <q-input
                outlined
                dense
                v-model="referenceDate"
                type="date"
                mask="YYYY/MM/DD"
                :max="maxDate"
                class="text-h6"
              />
            </div>
          </div>
        </div>

        <!-- 動態參數設定區域 -->
        <div class="row q-mb-lg" v-if="selectedMethod">
          <div class="col-12">
            <div class="text-h6 text-weight-bold q-mb-md">參數設定</div>

            <!-- 版路分析參數 -->
            <template v-if="selectedMethod === 'ball-follow'">
              <div class="row q-mb-md">
                <div class="col-12 text-h6">拖牌組合</div>
                <div class="col-12 col-sm-4 q-pa-sm">
                  <q-select
                    outlined
                    dense
                    v-model="analysisParams.ballComb1"
                    :options="combOptions"
                    emit-value
                    map-options
                  />
                </div>
                <div class="col-12 col-sm-4 q-pa-sm">
                  <q-select
                    outlined
                    dense
                    v-model="analysisParams.ballComb2"
                    :options="combOptions"
                    emit-value
                    map-options
                  />
                </div>
                <div class="col-12 col-sm-4 q-pa-sm">
                  <q-select
                    outlined
                    dense
                    v-model="analysisParams.ballComb3"
                    :options="combOptions"
                    emit-value
                    map-options
                  />
                </div>
              </div>
              <div class="row q-mb-md">
                <div class="col-12 col-sm-4">
                  <div class="text-h6">推算期數</div>
                  <div class="q-pa-sm">
                    <q-select
                      outlined
                      dense
                      v-model="analysisParams.periodNum"
                      :options="periodNumOpts"
                      emit-value
                      map-options
                    />
                  </div>
                </div>
                <div class="col-12 col-sm-4">
                  <div class="text-h6">最大區間</div>
                  <div class="q-pa-sm">
                    <q-select
                      outlined
                      dense
                      v-model="analysisParams.maxRange"
                      :options="maxRangeOpts"
                      emit-value
                      map-options
                    />
                  </div>
                </div>
                <div class="col-12 col-sm-4">
                  <div class="text-h6">預測期數</div>
                  <div class="q-pa-sm">
                    <q-select
                      outlined
                      dense
                      v-model="analysisParams.aheadNum"
                      :options="aheadNumOpts"
                      emit-value
                      map-options
                    />
                  </div>
                </div>
              </div>

              <!-- 篩選條件 -->
              <div class="row q-col-gutter-md">
                <div class="col-12 col-sm-4">
                  <div class="text-h6">連續拖出次數</div>
                  <div class="q-pa-sm">
                    <q-select
                      outlined
                      dense
                      v-model="filterParams.ballAccuracy"
                      :options="accuracyOpts"
                      map-options
                      emit-value
                    />
                  </div>
                </div>
                <div class="col-12 col-sm-4">
                  <div class="text-h6">篩選條件</div>
                  <div class="q-pa-sm">
                    <q-select
                      outlined
                      dense
                      v-model="filterParams.ballFilterCondition"
                      :options="filterConditionOpts"
                      map-options
                      emit-value
                    />
                  </div>
                </div>
                <div class="col-12 col-sm-4">
                  <div class="text-h6">分析數量</div>
                  <div class="q-pa-sm">
                    <q-select
                      outlined
                      dense
                      v-model="analysisParams.batchAnalysisRange"
                      :options="batchAnalysisRangeOpts"
                      map-options
                      emit-value
                    />
                  </div>
                </div>
              </div>
            </template>

            <!-- 尾數分析參數 -->
            <template v-if="selectedMethod === 'tail'">
              <div class="row q-mb-md">
                <div class="col-12 text-h6">拖牌組合</div>
                <div class="col-12 col-sm-4 q-pa-sm">
                  <q-select
                    outlined
                    dense
                    v-model="analysisParams.tailComb1"
                    :options="tailCombOptions"
                    emit-value
                    map-options
                  />
                </div>
                <div class="col-12 col-sm-4 q-pa-sm">
                  <q-select
                    outlined
                    dense
                    v-model="analysisParams.tailComb2"
                    :options="tailCombOptions"
                    emit-value
                    map-options
                  />
                </div>
                <div class="col-12 col-sm-4 q-pa-sm">
                  <q-select
                    outlined
                    dense
                    v-model="analysisParams.tailComb3"
                    :options="tailCombOptions"
                    emit-value
                    map-options
                  />
                </div>
              </div>
              <div class="row q-mb-md">
                <div class="col-12 col-sm-4">
                  <div class="text-h6">推算期數</div>
                  <div class="q-pa-sm">
                    <q-select
                      outlined
                      dense
                      v-model="analysisParams.periodNum"
                      :options="periodNumOpts"
                      emit-value
                      map-options
                    />
                  </div>
                </div>
                <div class="col-12 col-sm-4">
                  <div class="text-h6">最大區間</div>
                  <div class="q-pa-sm">
                    <q-select
                      outlined
                      dense
                      v-model="analysisParams.maxRange"
                      :options="maxRangeOpts"
                      emit-value
                      map-options
                    />
                  </div>
                </div>
                <div class="col-12 col-sm-4">
                  <div class="text-h6">預測期數</div>
                  <div class="q-pa-sm">
                    <q-select
                      outlined
                      dense
                      v-model="analysisParams.aheadNum"
                      :options="aheadNumOpts"
                      emit-value
                      map-options
                    />
                  </div>
                </div>
              </div>

              <!-- 篩選條件 -->
              <div class="row q-col-gutter-md">
                <div class="col-12 col-sm-4">
                  <div class="text-h6">連續拖出次數</div>
                  <div class="q-pa-sm">
                    <q-select
                      outlined
                      dense
                      v-model="filterParams.tailAccuracy"
                      :options="tailAccuracyOpts"
                      map-options
                      emit-value
                    />
                  </div>
                </div>
                <div class="col-12 col-sm-4">
                  <div class="text-h6">篩選條件</div>
                  <div class="q-pa-sm">
                    <q-select
                      outlined
                      dense
                      v-model="filterParams.tailFilterCondition"
                      :options="filterConditionOpts"
                      map-options
                      emit-value
                    />
                  </div>
                </div>
                <div class="col-12 col-sm-4">
                  <div class="text-h6">分析數量</div>
                  <div class="q-pa-sm">
                    <q-select
                      outlined
                      dense
                      v-model="analysisParams.batchAnalysisRange"
                      :options="batchAnalysisRangeOpts"
                      map-options
                      emit-value
                    />
                  </div>
                </div>
              </div>
            </template>

            <!-- 綜合分析參數 -->
            <template v-if="selectedMethod === 'pattern'">
              <div class="row q-mb-md">
                <div class="col-12 text-h6">版路拖牌組合</div>
                <div class="col-12 col-sm-4 q-pa-sm">
                  <q-select
                    outlined
                    dense
                    v-model="analysisParams.ballComb1"
                    :options="combOptions"
                    emit-value
                    map-options
                  />
                </div>
                <div class="col-12 col-sm-4 q-pa-sm">
                  <q-select
                    outlined
                    dense
                    v-model="analysisParams.ballComb2"
                    :options="combOptions"
                    emit-value
                    map-options
                  />
                </div>
                <div class="col-12 col-sm-4 q-pa-sm">
                  <q-select
                    outlined
                    dense
                    v-model="analysisParams.ballComb3"
                    :options="combOptions"
                    emit-value
                    map-options
                  />
                </div>
              </div>
              <div class="row q-mb-md">
                <div class="col-12 text-h6">尾數拖牌組合</div>
                <div class="col-12 col-sm-4 q-pa-sm">
                  <q-select
                    outlined
                    dense
                    v-model="analysisParams.tailComb1"
                    :options="tailCombOptions"
                    emit-value
                    map-options
                  />
                </div>
                <div class="col-12 col-sm-4 q-pa-sm">
                  <q-select
                    outlined
                    dense
                    v-model="analysisParams.tailComb2"
                    :options="tailCombOptions"
                    emit-value
                    map-options
                  />
                </div>
                <div class="col-12 col-sm-4 q-pa-sm">
                  <q-select
                    outlined
                    dense
                    v-model="analysisParams.tailComb3"
                    :options="tailCombOptions"
                    emit-value
                    map-options
                  />
                </div>
              </div>
              <div class="row q-mb-md">
                <div class="col-12 col-sm-4">
                  <div class="text-h6">推算期數</div>
                  <div class="q-pa-sm">
                    <q-select
                      outlined
                      dense
                      v-model="analysisParams.periodNum"
                      :options="periodNumOpts"
                      emit-value
                      map-options
                    />
                  </div>
                </div>
                <div class="col-12 col-sm-4">
                  <div class="text-h6">最大區間</div>
                  <div class="q-pa-sm">
                    <q-select
                      outlined
                      dense
                      v-model="analysisParams.maxRange"
                      :options="maxRangeOpts"
                      emit-value
                      map-options
                    />
                  </div>
                </div>
                <div class="col-12 col-sm-4">
                  <div class="text-h6">預測期數</div>
                  <div class="q-pa-sm">
                    <q-select
                      outlined
                      dense
                      v-model="analysisParams.aheadNum"
                      :options="aheadNumOpts"
                      emit-value
                      map-options
                    />
                  </div>
                </div>
              </div>

              <!-- 版路分析篩選條件 -->
              <div class="row q-mb-md">
                <div class="col-12 text-h6">版路分析篩選條件</div>
                <div class="col-12 col-sm-4">
                  <div class="text-h6">連續拖出次數</div>
                  <div class="q-pa-sm">
                    <q-select
                      outlined
                      dense
                      v-model="filterParams.ballAccuracy"
                      :options="accuracyOpts"
                      map-options
                      emit-value
                    />
                  </div>
                </div>
                <div class="col-12 col-sm-4">
                  <div class="text-h6">篩選條件</div>
                  <div class="q-pa-sm">
                    <q-select
                      outlined
                      dense
                      v-model="filterParams.ballFilterCondition"
                      :options="filterConditionOpts"
                      map-options
                      emit-value
                    />
                  </div>
                </div>
              </div>

              <!-- 尾數分析篩選條件 -->
              <div class="row q-mb-md">
                <div class="col-12 text-h6">尾數分析篩選條件</div>
                <div class="col-12 col-sm-4">
                  <div class="text-h6">連續拖出次數</div>
                  <div class="q-pa-sm">
                    <q-select
                      outlined
                      dense
                      v-model="filterParams.tailAccuracy"
                      :options="tailAccuracyOpts"
                      map-options
                      emit-value
                    />
                  </div>
                </div>
                <div class="col-12 col-sm-4">
                  <div class="text-h6">篩選條件</div>
                  <div class="q-pa-sm">
                    <q-select
                      outlined
                      dense
                      v-model="filterParams.tailFilterCondition"
                      :options="filterConditionOpts"
                      map-options
                      emit-value
                    />
                  </div>
                </div>
                <div class="col-12 col-sm-4">
                  <div class="text-h6">分析數量</div>
                  <div class="q-pa-sm">
                    <q-select
                      outlined
                      dense
                      v-model="analysisParams.batchAnalysisRange"
                      :options="batchAnalysisRangeOpts"
                      map-options
                      emit-value
                    />
                  </div>
                </div>
              </div>
            </template>
          </div>
        </div>

        <!-- 操作按鈕 -->
        <q-card-actions align="right" class="q-my-lg q-py-none q-px-md">
          <q-btn
            type="button"
            label="中斷計算"
            color="negative"
            @click="stopBatchAnalysis"
            class="text-h6 q-mr-md"
            v-if="isCalculating"
          />
          <q-btn
            type="button"
            label="產生報表"
            color="positive"
            class="text-h6 q-px-lg q-py-sm"
            @click="startBatchAnalysis"
            :loading="isCalculating"
            :disable="!canStartAnalysis"
          >
            <template v-slot:loading>
              <q-spinner-dots />
            </template>
          </q-btn>
        </q-card-actions>
      </q-card-section>

      <!-- 進度條 -->
      <q-card-section v-if="isCalculating">
        <div class="text-center q-mb-sm">
          {{ progressMessage }}
        </div>
        <q-linear-progress
          rounded
          size="md"
          :value="progress"
          :animation-speed="50"
          color="primary"
          class="q-mb-xs"
        />
      </q-card-section>
    </q-card>

    <!-- 結果顯示區域 -->
    <q-card v-if="batchResults.length > 0 && !isCalculating" class="q-mt-lg">
      <q-card-section>
        <div class="text-h6 text-weight-bold q-mb-md">分析結果</div>
        <div class="row q-mb-md">
          <div class="col-12 col-sm-6">
            <div class="text-subtitle1">
              分析方法：{{ getMethodName(selectedMethod) }}
            </div>
            <div class="text-subtitle1">
              彩種：{{ getLottoTypeName(selectedLottoType) }}
            </div>
          </div>
          <div class="col-12 col-sm-6 text-right">
            <q-btn
              color="primary"
              icon="download"
              :label="`下載 ${outputFormat.toUpperCase()} 檔案`"
              @click="downloadResults"
              :disable="batchResults.length === 0"
            />
          </div>
        </div>


      </q-card-section>
    </q-card>
  </q-page>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue';
import { Notify } from 'quasar';
import * as XLSX from 'xlsx';
import ExcelJS from 'exceljs';
import { LOTTO_API } from '@/api';
import { LottoItem } from '@/api/modules/lotto';
import { useLotteryAnalysis } from '@/composables/useLotteryAnalysis';
import { handleError } from '@/utils';
import { AnalysisParameters, BallFollowParameters, TailParameters } from '@/models/batch-analysis';
import { StatResult, Occurrence } from '@/models/types';

const analysis = useLotteryAnalysis();

// 分析方法選項
const analysisMethodOptions = [
  { label: '版路分析', value: 'ball-follow' },
  { label: '尾數分析', value: 'tail' },
  { label: '綜合分析', value: 'pattern' }
];

// 彩種選項
const lottoTypeOptions = [
  { label: '威力彩', value: 'super_lotto638' },
  { label: '大樂透', value: 'lotto649' },
  { label: '今彩539', value: 'daily539' },
  { label: '六合彩', value: 'lotto_hk' }
];

// 響應式數據
const selectedMethod = ref('ball-follow');
const selectedLottoType = ref('super_lotto638');
const referenceDate = ref('');
const outputFormat = ref('excel');
const isCalculating = ref(false);
const progress = ref(0);
const progressMessage = ref('');
const batchResults = ref<BatchAnalysisResult[]>([]);

// 統一的分析參數
const analysisParams = ref({
  // 版路拖牌組合
  ballComb1: 1,
  ballComb2: 1,
  ballComb3: 1,
  // 尾數拖牌組合
  tailComb1: 1,
  tailComb2: 1,
  tailComb3: 1,
  // 期數相關參數
  periodNum: 50,
  maxRange: 20,
  aheadNum: 1,
  // 分析數量（獨立參數）
  batchAnalysisRange: 100
});

// 統一的篩選條件
const filterParams = ref({
  // 版路分析篩選條件
  ballAccuracy: 1,
  ballFilterCondition: 'above',
  // 尾數分析篩選條件
  tailAccuracy: 1,
  tailFilterCondition: 'above'
});

// 篩選條件選項
const accuracyOpts = ref([
  { label: '已連續拖出 1 次', value: 1 },
  { label: '已連續拖出 2 次', value: 2 },
  { label: '已連續拖出 3 次', value: 3 },
  { label: '已連續拖出 4 次', value: 4 },
  { label: '已連續拖出 5 次', value: 5 },
]);

const tailAccuracyOpts = ref([
  { label: '已連續拖出 1 次', value: 1 },
  { label: '已連續拖出 2 次', value: 2 },
  { label: '已連續拖出 3 次', value: 3 },
  { label: '已連續拖出 4 次', value: 4 },
  { label: '已連續拖出 5 次', value: 5 },
  { label: '已連續拖出 6 次', value: 6 },
  { label: '已連續拖出 7 次', value: 7 },
  { label: '已連續拖出 8 次', value: 8 },
  { label: '已連續拖出 9 次', value: 9 },
  { label: '已連續拖出 10 次', value: 10 },
]);

const filterConditionOpts = ref([
  { label: '(含)以上', value: 'above' },
  { label: '(含)以下', value: 'below' },
  { label: '剛好', value: 'exact' },
]);

// 批量分析結果類型定義
interface BatchAnalysisResult {
  date: string;
  period: string;
  analysisType: string;
  ballFollowResults?: StatResult[];
  tailResults?: StatResult[];
  ballFollowOccurrences?: Map<string, Occurrence>;
  tailOccurrences?: Map<string, Occurrence>;
  predictNumbers?: number[];
  actualNumbers?: number[];
  matches?: number[];
  // 新增詳細分析結果
  targetNumAppearances?: Map<number, number>;
  nonAppearedNumbers?: number[];
  nonAppearedByFrequency?: Map<number, number>;
  tailNumAppearances?: Map<number, number>;
  // 預測響應數據
  predictResponse?: {
    draw_date?: string;
    period?: string;
    draw_number_size?: number[];
    special_number?: number;
  };
  // 綜合分析專用數據
  rdTailAppearances?: Map<number, number>;      // 版路分析尾數
  tailRdScoreMap?: Map<number, number>;         // 尾數分析尾數
  tailScoreMap?: Map<number, number>;           // 綜合分析尾數
  tailMatchResults?: number[];                  // 版路+尾數比對結果
  tailMatchResults2?: number[];                 // 版路+綜合比對結果
  tailMatchResults3?: number[];                 // 尾數+綜合比對結果
  // 尾數分析專用統計數據
  tailAnalysisAppearances?: Map<number, number>; // 純尾數分析的統計數據
}

// 當前日期（用於限制參考期號）
const maxDate = computed(() => {
  return new Date().toISOString().split('T')[0];
});

// 檢查是否可以開始分析
const canStartAnalysis = computed(() => {
  return selectedMethod.value &&
         selectedLottoType.value &&
         referenceDate.value &&
         !isCalculating.value;
});

// 獎號組合
const combOptions = [
  { label: '一星組合', value: 1 },
  { label: '二星組合', value: 2 },
  { label: '三星組合', value: 3 },
];
// 尾數組合
const tailCombOptions = [
  { label: '一星組合', value: 1 },
  { label: '二星組合', value: 2 },
  { label: '三星組合', value: 3 },
  { label: '四星組合', value: 4 },
  { label: '五星組合', value: 5 },
];

const periodNumOpts = ref(
  Array.from({ length: 1991 }, (_, i) => ({
    label: `${i + 10}期`,
    value: i + 10,
  }))
);

const maxRangeOpts = ref(
  Array.from({ length: 21 }, (_, i) => ({
    label: `${i + 10}期`,
    value: i + 10,
  }))
);

const aheadNumOpts = ref(
  Array.from({ length: 15 }, (_, i) => ({
    label: `下${i + 1}期`,
    value: i + 1,
  }))
);

const batchAnalysisRangeOpts = ref(
  Array.from({ length: 200 }, (_, i) => ({
    label: `${i + 1}期`,
    value: i + 1,
  }))
);



// 方法
const getMethodName = (method: string) => {
  const methodMap: Record<string, string> = {
    'ball-follow': '版路分析',
    'tail': '尾數分析',
    'pattern': '綜合分析'
  };
  return methodMap[method] || method;
};

const getLottoTypeName = (type: string) => {
  const typeMap: Record<string, string> = {
    'super_lotto638': '威力彩',
    'lotto649': '大樂透',
    'daily539': '今彩539',
    'lotto_hk': '六合彩'
  };
  return typeMap[type] || type;
};

const onLottoTypeChange = () => {
  // 當彩種改變時，可以在這裡更新相關設定
};

const getAnalysisParameters = (): AnalysisParameters => {
  switch (selectedMethod.value) {
    case 'ball-follow':
      return {
        comb1: analysisParams.value.ballComb1,
        comb2: analysisParams.value.ballComb2,
        comb3: analysisParams.value.ballComb3,
        periodNum: analysisParams.value.periodNum,
        maxRange: analysisParams.value.maxRange,
        aheadNum: analysisParams.value.aheadNum
      };
    case 'tail':
      return {
        tailComb1: analysisParams.value.tailComb1,
        tailComb2: analysisParams.value.tailComb2,
        tailComb3: analysisParams.value.tailComb3,
        periodNum: analysisParams.value.periodNum,
        maxRange: analysisParams.value.maxRange,
        aheadNum: analysisParams.value.aheadNum
      };
    case 'pattern':
      return {
        comb1: analysisParams.value.ballComb1,
        comb2: analysisParams.value.ballComb2,
        comb3: analysisParams.value.ballComb3,
        tailComb1: analysisParams.value.tailComb1,
        tailComb2: analysisParams.value.tailComb2,
        tailComb3: analysisParams.value.tailComb3,
        periodNum: analysisParams.value.periodNum,
        maxRange: analysisParams.value.maxRange,
        aheadNum: analysisParams.value.aheadNum
      };
    default:
      // Return default parameters as fallback
      return {
        comb1: 1,
        comb2: 1,
        comb3: 1,
        tailComb1: 1,
        tailComb2: 1,
        tailComb3: 1,
        periodNum: 50,
        maxRange: 20,
        aheadNum: 1
      };
  }
};

const isSuperLotto = ref(false);

// 輔助函數：從分析結果中提取預測號碼和詳細統計
const extractDetailedAnalysis = (results: StatResult[], maxNumber = 49, accuracyFilter = 1, filterConditionValue = 'above') => {
  // 篩選符合條件的結果
  const filteredResults = results.filter(result => {
    switch (filterConditionValue) {
      case 'above':
        return result.consecutiveHits >= accuracyFilter;
      case 'below':
        return result.consecutiveHits <= accuracyFilter;
      case 'exact':
        return result.consecutiveHits === accuracyFilter;
      default:
        return result.consecutiveHits >= accuracyFilter;
    }
  });

  const targetNumAppearances = new Map<number, number>();
  const tailNumAppearances = new Map<number, number>();
  const nonAppearedFrequency = new Map<number, number>();

  // 統計預測號碼出現次數
  filteredResults.forEach(result => {
    result.targetNumbers.forEach(num => {
      targetNumAppearances.set(num, (targetNumAppearances.get(num) || 0) + 1);
      // 統計尾數
      const tail = num % 10;
      tailNumAppearances.set(tail, (tailNumAppearances.get(tail) || 0) + 1);
    });
  });

  // 計算未出現號碼
  const nonAppearedNumbers: number[] = [];
  for (let i = 1; i <= maxNumber; i++) {
    if (!targetNumAppearances.has(i)) {
      nonAppearedNumbers.push(i);
      // 統計未出現號碼在所有結果中的頻率
      const frequency = results.filter(result => result.targetNumbers.includes(i)).length;
      nonAppearedFrequency.set(i, frequency);
    }
  }

  // 取前10個最高機率的預測號碼
  const topPredictNumbers = Array.from(targetNumAppearances.entries())
    .sort((a, b) => b[1] - a[1])
    .slice(0, 10)
    .map(([num]) => num);

  return {
    predictNumbers: topPredictNumbers,
    targetNumAppearances,
    nonAppearedNumbers,
    nonAppearedByFrequency: nonAppearedFrequency,
    tailNumAppearances
  };
};

// 輔助函數：找出預測號碼與實際號碼的匹配
const findMatches = (predictNumbers: number[], actualNumbers: number[]): number[] => {
  return predictNumbers.filter(num => actualNumbers.includes(num));
};

// 輔助函數：獲取預測日期
const getPredictDate = (result: BatchAnalysisResult): string => {
  if (result.predictResponse) {
    if (result.predictResponse.draw_date) {
      return result.predictResponse.draw_date;
    } else {
      return '尚未開獎';
    }
  } else {
    return '尚未開獎';
  }
};

// 輔助函數：獲取彩種的最大號碼
const getMaxNumberForLottoType = (lottoType: string): number => {
  switch (lottoType) {
    case 'super_lotto638':
      return 38;
    case 'lotto649':
      return 49;
    case 'daily539':
      return 39;
    case 'lotto_hk':
      return 49;
    default:
      return 49;
  }
};

// 輔助函數：將號碼分成每10個一列的陣列
const formatNumbersIntoRows = (numbers: number[], isForTail = false): string[][] => {
  const rows: string[][] = [];
  for (let i = 0; i < numbers.length; i += 10) {
    const rowNumbers = numbers.slice(i, i + 10);
    const formattedNumbers = rowNumbers.map(n =>
      isForTail ? n.toString() : n.toString().padStart(2, '0')
    );
    rows.push(formattedNumbers);
  }
  return rows;
};

// 輔助函數：應用紅色字體樣式給匹配的號碼（使用標記方式）
const applyRedFontForMatches = (worksheet: XLSX.WorkSheet, sheetData: (string | number)[][], type: 'predict' | 'tail') => {
  console.log('開始應用紅色字體標記，類型:', type);
  console.log('批量結果數量:', batchResults.value.length);

  // 遍歷數據行，從第4行開始（跳過標題行）
  for (let rowIndex = 3; rowIndex < sheetData.length; rowIndex++) {
    const row = sheetData[rowIndex];
    const dateCell = row[0];

    // 跳過空白日期行（換列對齊的行）
    if (!dateCell || dateCell === '') continue;

    // 找到對應的批量分析結果
    const result = batchResults.value.find(r => r.date === dateCell);
    if (!result || !result.actualNumbers) {
      console.log('找不到對應結果，日期:', dateCell);
      continue;
    }

    console.log('處理日期:', dateCell, '實際號碼:', result.actualNumbers);

    // 檢查每個號碼是否與實際開獎號碼匹配（從第3欄開始，跳過分析日期和預測日期）
    for (let colIndex = 2; colIndex < row.length; colIndex++) {
      const cellValue = row[colIndex];
      if (cellValue && cellValue !== '') {
        let isMatch = false;

        if (type === 'predict') {
          // 預測號碼：直接比較
          const numberToCheck = parseInt(cellValue.toString().replace(/^0+/, ''), 10);
          isMatch = result.actualNumbers.includes(numberToCheck);
          console.log('檢查預測號碼:', numberToCheck, '是否匹配:', isMatch);
        } else {
          // 尾數：比較尾數
          const tailToCheck = parseInt(cellValue.toString(), 10);
          const actualTails = result.actualNumbers.map(n => n % 10);
          isMatch = actualTails.includes(tailToCheck);
          console.log('檢查尾數:', tailToCheck, '實際尾數:', actualTails, '是否匹配:', isMatch);
        }

        if (isMatch) {
          const cellRef = XLSX.utils.encode_cell({ r: rowIndex, c: colIndex });
          console.log('標記命中號碼，儲存格:', cellRef, '值:', cellValue);

          // 使用簡單標記方式（在值後面加上符號）
          const markedValue = `${cellValue}★`;

          // 更新工作表數據
          if (!worksheet[cellRef]) {
            worksheet[cellRef] = { v: markedValue, t: 's' };
          } else {
            worksheet[cellRef].v = markedValue;
          }

          // 同時更新原始數據
          sheetData[rowIndex][colIndex] = markedValue;
        }
      }
    }
  }

  console.log('紅色字體標記應用完成');
};

// 創建參數設定工作表
const createParametersSheet = (workbook: XLSX.WorkBook, analysisType: string) => {
  const sheetData: (string | number)[][] = [];

  // 標題
  const methodName = getMethodName(selectedMethod.value);
  const lottoName = getLottoTypeName(selectedLottoType.value);
  sheetData.push([`${methodName} - 參數設定`]);
  sheetData.push([]);

  // 基本設定
  sheetData.push(['基本設定']);
  sheetData.push(['彩種', lottoName]);
  sheetData.push(['分析方法', methodName]);
  sheetData.push([]);

  // 根據分析類型添加對應的參數
  switch (analysisType) {
    case 'ball':
      // 版路分析參數
      sheetData.push(['版路分析參數']);
      sheetData.push(['分析數量', analysisParams.value.batchAnalysisRange]);
      sheetData.push(['拖牌組合', `${analysisParams.value.ballComb1}-${analysisParams.value.ballComb2}-${analysisParams.value.ballComb3}`]);
      sheetData.push(['推算期數', analysisParams.value.periodNum]);
      sheetData.push(['最大區間', analysisParams.value.maxRange]);
      sheetData.push(['預測期數', analysisParams.value.aheadNum]);
      sheetData.push([]);

      // 篩選條件
      sheetData.push(['篩選條件']);
      const accuracyLabel = accuracyOpts.value.find(opt => opt.value === filterParams.value.ballAccuracy)?.label || filterParams.value.ballAccuracy;
      const filterLabel = filterConditionOpts.value.find(opt => opt.value === filterParams.value.ballFilterCondition)?.label || filterParams.value.ballFilterCondition;
      sheetData.push(['連續拖出次數', accuracyLabel]);
      sheetData.push(['篩選條件', filterLabel]);
      break;

    case 'tail':
      // 尾數分析參數
      sheetData.push(['尾數分析參數']);
      sheetData.push(['分析數量', analysisParams.value.batchAnalysisRange]);
      sheetData.push(['拖牌組合', `${analysisParams.value.tailComb1}-${analysisParams.value.tailComb2}-${analysisParams.value.tailComb3}`]);
      sheetData.push(['推算期數', analysisParams.value.periodNum]);
      sheetData.push(['最大區間', analysisParams.value.maxRange]);
      sheetData.push(['預測期數', analysisParams.value.aheadNum]);
      sheetData.push([]);

      // 篩選條件
      sheetData.push(['篩選條件']);
      const tailAccuracyLabel = tailAccuracyOpts.value.find(opt => opt.value === filterParams.value.tailAccuracy)?.label || filterParams.value.tailAccuracy;
      const tailFilterLabel = filterConditionOpts.value.find(opt => opt.value === filterParams.value.tailFilterCondition)?.label || filterParams.value.tailFilterCondition;
      sheetData.push(['連續拖出次數', tailAccuracyLabel]);
      sheetData.push(['篩選條件', tailFilterLabel]);
      break;

    case 'pattern':
      // 綜合分析參數
      sheetData.push(['綜合分析參數']);
      sheetData.push(['分析數量', analysisParams.value.batchAnalysisRange]);
      sheetData.push([]);

      sheetData.push(['版路分析參數']);
      sheetData.push(['拖牌組合', `${analysisParams.value.ballComb1}-${analysisParams.value.ballComb2}-${analysisParams.value.ballComb3}`]);
      sheetData.push(['推算期數', analysisParams.value.periodNum]);
      sheetData.push(['最大區間', analysisParams.value.maxRange]);
      sheetData.push([]);

      sheetData.push(['尾數分析參數']);
      sheetData.push(['拖牌組合', `${analysisParams.value.tailComb1}-${analysisParams.value.tailComb2}-${analysisParams.value.tailComb3}`]);
      sheetData.push(['推算期數', analysisParams.value.periodNum]);
      sheetData.push(['最大區間', analysisParams.value.maxRange]);
      sheetData.push([]);

      sheetData.push(['預測期數', analysisParams.value.aheadNum]);
      sheetData.push([]);

      // 版路分析篩選條件
      sheetData.push(['版路分析篩選條件']);
      const patternBallAccuracyLabel = accuracyOpts.value.find(opt => opt.value === filterParams.value.ballAccuracy)?.label || filterParams.value.ballAccuracy;
      const patternBallFilterLabel = filterConditionOpts.value.find(opt => opt.value === filterParams.value.ballFilterCondition)?.label || filterParams.value.ballFilterCondition;
      sheetData.push(['連續拖出次數', patternBallAccuracyLabel]);
      sheetData.push(['篩選條件', patternBallFilterLabel]);
      sheetData.push([]);

      // 尾數分析篩選條件
      sheetData.push(['尾數分析篩選條件']);
      const patternTailAccuracyLabel = tailAccuracyOpts.value.find(opt => opt.value === filterParams.value.tailAccuracy)?.label || filterParams.value.tailAccuracy;
      const patternTailFilterLabel = filterConditionOpts.value.find(opt => opt.value === filterParams.value.tailFilterCondition)?.label || filterParams.value.tailFilterCondition;
      sheetData.push(['連續拖出次數', patternTailAccuracyLabel]);
      sheetData.push(['篩選條件', patternTailFilterLabel]);
      break;
  }

  sheetData.push([]);

  // 分析時間
  sheetData.push(['分析資訊']);
  sheetData.push(['分析時間', new Date().toLocaleString('zh-TW')]);
  sheetData.push(['分析期數', batchResults.value.length]);

  const worksheet = XLSX.utils.aoa_to_sheet(sheetData);

  // 設置列寬
  const colWidths = [
    { wch: 20 }, // 參數名稱欄
    { wch: 30 }  // 參數值欄
  ];
  worksheet['!cols'] = colWidths;

  // 設置樣式 - 標題行加粗
  const titleRows = [0, 4, 9, 14]; // 根據不同分析類型調整
  titleRows.forEach(rowIndex => {
    if (rowIndex < sheetData.length) {
      const cellRef = XLSX.utils.encode_cell({ r: rowIndex, c: 0 });
      if (worksheet[cellRef]) {
        worksheet[cellRef].s = {
          font: { bold: true, sz: 12 },
          alignment: { horizontal: 'left' }
        };
      }
    }
  });

  XLSX.utils.book_append_sheet(workbook, worksheet, '參數設定');
};

// 計算綜合分析專用數據（參考 PatternResult.vue 的邏輯）
const calculatePatternAnalysisData = (
  ballFollowResults: StatResult[],
  tailResults: StatResult[]
) => {
  // 初始化數據結構
  const rdTailAppearances = new Map<number, number>();
  const tailRdScoreMap = new Map<number, number>();
  const tailScoreMap = new Map<number, number>();

  // 初始化尾數分數
  for (let i = 0; i < 10; i++) {
    tailScoreMap.set(i, 0);
  }

  // 計算版路分析尾數 (rdTailAppearances)
  const ballFilteredResults = ballFollowResults.filter(result => {
    switch (filterParams.value.ballFilterCondition) {
      case 'above': return result.consecutiveHits >= filterParams.value.ballAccuracy;
      case 'below': return result.consecutiveHits <= filterParams.value.ballAccuracy;
      case 'exact': return result.consecutiveHits === filterParams.value.ballAccuracy;
      default: return true;
    }
  });

  for (const result of ballFilteredResults) {
    const tailSet = new Set<number>();
    for (const number of result.targetNumbers) {
      const tailNumber = number % 10;
      tailSet.add(tailNumber);
    }
    for (const tailNumber of tailSet) {
      rdTailAppearances.set(tailNumber, (rdTailAppearances.get(tailNumber) || 0) + 1);
    }
  }

  // 計算尾數分析尾數 (tailRdScoreMap)
  const tailFilteredResults = tailResults.filter(result => {
    switch (filterParams.value.tailFilterCondition) {
      case 'above': return result.consecutiveHits >= filterParams.value.tailAccuracy;
      case 'below': return result.consecutiveHits <= filterParams.value.tailAccuracy;
      case 'exact': return result.consecutiveHits === filterParams.value.tailAccuracy;
      default: return true;
    }
  });

  for (const result of tailFilteredResults) {
    for (const tailNumber of result.targetNumbers) {
      tailRdScoreMap.set(tailNumber, (tailRdScoreMap.get(tailNumber) || 0) + 1);
    }
  }

  // 計算綜合分析尾數 (tailScoreMap) - 結合版路和尾數分析的分數
  const rdTotalCount = Math.max(1, ballFilteredResults.reduce((sum, r) => sum + r.consecutiveHits, 0));
  const tailTotalCount = Math.max(1, tailFilteredResults.reduce((sum, r) => sum + r.consecutiveHits, 0));

  // 版路分析貢獻
  for (const [tailNumber, count] of rdTailAppearances.entries()) {
    const score = count / rdTotalCount;
    tailScoreMap.set(tailNumber, (tailScoreMap.get(tailNumber) || 0) + score);
  }

  // 尾數分析貢獻
  for (const [tailNumber, score] of tailRdScoreMap.entries()) {
    const normalizedScore = score / tailTotalCount;
    tailScoreMap.set(tailNumber, (tailScoreMap.get(tailNumber) || 0) + normalizedScore);
  }

  // 標準化綜合分數
  for (const [tailNumber, score] of tailScoreMap.entries()) {
    tailScoreMap.set(tailNumber, Number(((score / 2) * 100).toFixed(1)));
  }

  // 排序所有Map
  const sortedRdTailAppearances = new Map([...rdTailAppearances.entries()].sort((a, b) => b[1] - a[1]));
  const sortedTailRdScoreMap = new Map([...tailRdScoreMap.entries()].sort((a, b) => b[1] - a[1]));
  const sortedTailScoreMap = new Map([...tailScoreMap.entries()].sort((a, b) => b[1] - a[1]));

  // 計算比對結果
  const rdTails = Array.from(sortedRdTailAppearances.keys()).slice(0, 5);
  const tails = Array.from(sortedTailRdScoreMap.keys()).slice(0, 5);
  const patterns = Array.from(sortedTailScoreMap.keys()).slice(0, 5);

  const tailMatchResults: number[] = [];
  const tailMatchResults2: number[] = [];
  const tailMatchResults3: number[] = [];

  // 版路+尾數
  for (const tailNumber of rdTails) {
    if (tails.includes(tailNumber)) {
      tailMatchResults.push(tailNumber);
    }
  }

  // 版路+綜合
  for (const tailNumber of rdTails) {
    if (patterns.includes(tailNumber)) {
      tailMatchResults2.push(tailNumber);
    }
  }

  // 尾數+綜合
  for (const tailNumber of tails) {
    if (patterns.includes(tailNumber)) {
      tailMatchResults3.push(tailNumber);
    }
  }

  // 排序比對結果 (0排在最後面，其他最小排前面)
  const sortTails = (a: number, b: number) => (a === 0 ? 1 : b === 0 ? -1 : a - b);
  tailMatchResults.sort(sortTails);
  tailMatchResults2.sort(sortTails);
  tailMatchResults3.sort(sortTails);

  return {
    rdTailAppearances: sortedRdTailAppearances,
    tailRdScoreMap: sortedTailRdScoreMap,
    tailScoreMap: sortedTailScoreMap,
    tailMatchResults,
    tailMatchResults2,
    tailMatchResults3
  };
};

const startBatchAnalysis = async () => {
  if (!canStartAnalysis.value) {
    Notify.create({
      type: 'warning',
      message: '請完整填寫所有必要參數'
    });
    return;
  }

  try {
    isCalculating.value = true;
    isSuperLotto.value = selectedLottoType.value === 'super_lotto638';
    progress.value = 0;
    progressMessage.value = '準備開始...';
    batchResults.value = []; // 清空之前的結果

    // 準備API請求參數 - 獲取要分析的期數
    const analysisCount = analysisParams.value.batchAnalysisRange;

    const response = await LOTTO_API.getLottoList({
      draw_type: selectedLottoType.value,
      date_end: referenceDate.value,
      limit: analysisCount
    });
    // 將結果反轉，讓時間由舊到新排列
    const referenceResults = response.data.reverse();

    progressMessage.value = '正在進行分析...';

    // 進行分析
    let count = 0;
    for (const result of referenceResults) {
      // 更新進度條
      progress.value = count / referenceResults.length;
      progressMessage.value = `分析中... ${++count}/${referenceResults.length}`;

      // 獲取分析用的歷史數據
      const historyResponse = await LOTTO_API.getLottoList({
        draw_type: selectedLottoType.value,
        date_end: result.draw_date,
        limit: analysisCount
      });

      // 獲取預測期的實際開獎結果
      const params = getAnalysisParameters();
      const aheadCount = params.aheadNum;
      const predictResponse = await LOTTO_API.getLottoPredict({
        draw_type: selectedLottoType.value,
        draw_date: result.draw_date,
        ahead_count: aheadCount
      });

      // 組合實際開獎號碼（包含特別號）
      let actualNumbers: number[] = [];
      if (predictResponse.data) {
        actualNumbers = [...(predictResponse.data.draw_number_size || [])];
        // 如果有特別號且不是威力彩，則添加特別號
        if (!isSuperLotto.value && predictResponse.data.special_number) {
          actualNumbers.push(predictResponse.data.special_number);
        }
      }

      let batchResult: BatchAnalysisResult = {
        date: result.draw_date,
        period: result.period,
        analysisType: selectedMethod.value,
        actualNumbers: actualNumbers,
        // 保存預測響應數據以供Excel使用
        predictResponse: predictResponse.data
      };

      switch (selectedMethod.value) {
        case 'ball-follow':
          const ballFollowAnalysis = await doRdCalculating(historyResponse.data);
          batchResult.ballFollowResults = ballFollowAnalysis.data;
          batchResult.ballFollowOccurrences = ballFollowAnalysis.occurrences;

          // 獲取彩種的最大號碼
          const maxNumber = getMaxNumberForLottoType(selectedLottoType.value);
          const detailedAnalysis = extractDetailedAnalysis(
            ballFollowAnalysis.data,
            maxNumber,
            filterParams.value.ballAccuracy,
            filterParams.value.ballFilterCondition
          );

          batchResult.predictNumbers = detailedAnalysis.predictNumbers;
          batchResult.targetNumAppearances = detailedAnalysis.targetNumAppearances;
          batchResult.nonAppearedNumbers = detailedAnalysis.nonAppearedNumbers;
          batchResult.nonAppearedByFrequency = detailedAnalysis.nonAppearedByFrequency;
          batchResult.tailNumAppearances = detailedAnalysis.tailNumAppearances;
          batchResult.matches = findMatches(batchResult.predictNumbers || [], batchResult.actualNumbers || []);
          break;
        case 'tail':
          const tailAnalysis = await doTailRdCalculating(historyResponse.data);
          batchResult.tailResults = tailAnalysis.data;
          batchResult.tailOccurrences = tailAnalysis.occurrences;

          const tailDetailedAnalysis = extractDetailedAnalysis(
            tailAnalysis.data,
            10, // 尾數最大為9
            filterParams.value.tailAccuracy,
            filterParams.value.tailFilterCondition
          );

          batchResult.predictNumbers = tailDetailedAnalysis.predictNumbers;
          batchResult.targetNumAppearances = tailDetailedAnalysis.targetNumAppearances;
          batchResult.nonAppearedNumbers = tailDetailedAnalysis.nonAppearedNumbers;
          batchResult.nonAppearedByFrequency = tailDetailedAnalysis.nonAppearedByFrequency;
          batchResult.tailNumAppearances = tailDetailedAnalysis.tailNumAppearances;
          batchResult.matches = findMatches(batchResult.predictNumbers || [], batchResult.actualNumbers || []);
          break;
        case 'pattern':
          const ballAnalysis = await doRdCalculating(historyResponse.data);
          const tailAnalysis2 = await doTailRdCalculating(historyResponse.data);
          batchResult.ballFollowResults = ballAnalysis.data;
          batchResult.ballFollowOccurrences = ballAnalysis.occurrences;
          batchResult.tailResults = tailAnalysis2.data;
          batchResult.tailOccurrences = tailAnalysis2.occurrences;

          // 獲取彩種的最大號碼
          const maxNumber2 = getMaxNumberForLottoType(selectedLottoType.value);

          // 版路分析詳細統計
          const ballDetailedAnalysis = extractDetailedAnalysis(
            ballAnalysis.data,
            maxNumber2,
            filterParams.value.ballAccuracy,
            filterParams.value.ballFilterCondition
          );

          // 尾數分析詳細統計
          const tailDetailedAnalysis2 = extractDetailedAnalysis(
            tailAnalysis2.data,
            10,
            filterParams.value.tailAccuracy,
            filterParams.value.tailFilterCondition
          );

          // 綜合分析的預測號碼需要結合版路和尾數分析
          const ballPredictNumbers = ballDetailedAnalysis.predictNumbers;
          const tailPredictNumbers = tailDetailedAnalysis2.predictNumbers;
          batchResult.predictNumbers = [...new Set([...ballPredictNumbers, ...tailPredictNumbers])];

          // 合併統計數據（使用版路分析的數據作為主要統計）
          batchResult.targetNumAppearances = ballDetailedAnalysis.targetNumAppearances;
          batchResult.nonAppearedNumbers = ballDetailedAnalysis.nonAppearedNumbers;
          batchResult.nonAppearedByFrequency = ballDetailedAnalysis.nonAppearedByFrequency;
          batchResult.tailNumAppearances = ballDetailedAnalysis.tailNumAppearances;

          // 存儲尾數分析的專用統計數據
          batchResult.tailAnalysisAppearances = tailDetailedAnalysis2.tailNumAppearances;

          // 計算綜合分析專用數據
          const patternAnalysisData = calculatePatternAnalysisData(
            ballAnalysis.data,
            tailAnalysis2.data
          );

          batchResult.rdTailAppearances = patternAnalysisData.rdTailAppearances;
          batchResult.tailRdScoreMap = patternAnalysisData.tailRdScoreMap;
          batchResult.tailScoreMap = patternAnalysisData.tailScoreMap;
          batchResult.tailMatchResults = patternAnalysisData.tailMatchResults;
          batchResult.tailMatchResults2 = patternAnalysisData.tailMatchResults2;
          batchResult.tailMatchResults3 = patternAnalysisData.tailMatchResults3;

          batchResult.matches = findMatches(batchResult.predictNumbers || [], batchResult.actualNumbers || []);
          break;
      }

      batchResults.value.push(batchResult);
    }

    progressMessage.value = '分析完成！';
    progress.value = 1;

    Notify.create({
      type: 'positive',
      position: 'top',
      message: '分析完成！'
    });

  } catch (error) {
    handleError(error);
  } finally {
    isCalculating.value = false;
  }
};

const stopBatchAnalysis = () => {
  analysis.stopAnalyzer();
  batchResults.value = [];
  isCalculating.value = false;
  progressMessage.value = '分析已中斷';

  Notify.create({
    type: 'warning',
    message: '分析已中斷'
  });
};

// 獎號拖牌
const doRdCalculating = async (drawResults: LottoItem[]) => {
  const params = getAnalysisParameters() as BallFollowParameters;
  const serializedDrawResults = drawResults.map((item: LottoItem) => {
    const numbers = [...item.draw_number_size];
    if (item.special_number && !isSuperLotto.value) {
      // 除威力彩，其餘有彩種皆須加入特別號
      numbers.push(item.special_number);
    }
    // 返回一個純粹的對象
    return {
      numbers: [...numbers], // 確保創建新數組
      period: String(item.period), // 確保 period 是字符串
    };
  }).reverse();

  // 設置配置
  analysis.init(
    {
      firstGroupSize: params.comb1 || 1,
      secondGroupSize: params.comb2 || 1,
      targetGroupSize: params.comb3 || 1,
      maxRange: params.maxRange || 20,
      lookAheadCount: params.aheadNum || 1,
    },
    serializedDrawResults
  );

  return await analysis.analyzeWithProgress();
};

// 尾數拖牌
const doTailRdCalculating = async (drawResults: LottoItem[]) => {
  const params = getAnalysisParameters() as TailParameters;

  // 創建數據結構
  const serializedTailResults = drawResults.map((item: LottoItem) => {
    const numbers = new Set<number>();

    for (let number of item.draw_number_size) {
      numbers.add(number % 10);
    }

    if (item.special_number && !isSuperLotto.value) {
      numbers.add(item.special_number % 10);
    }

    const sorted = Array.from(numbers).sort((a, b) => {
      if (a === 0) return 1;
      if (b === 0) return -1;
      return a - b;
    });

    return {
      period: String(item.period),
      numbers: [...sorted],
    };
  }).reverse();

  analysis.init(
    {
      firstGroupSize: params.tailComb1 || 1,
      secondGroupSize: params.tailComb2 || 1,
      targetGroupSize: params.tailComb3 || 1,
      maxRange: params.maxRange || 20,
      lookAheadCount: params.aheadNum || 1,
    },
    serializedTailResults
  );

  return await analysis.analyzeWithProgress();
};

const downloadResults = () => {
  // if (batchResults.value.length === 0) {
  //   Notify.create({
  //     type: 'warning',
  //     message: '沒有可下載的結果'
  //   });
  //   return;
  // }

  try {
      downloadExcel();
  } catch (error) {
    console.error('下載失敗:', error);
    Notify.create({
      type: 'negative',
      message: '檔案下載失敗'
    });
  }
};

// 暫時保留原有的 XLSX 版本作為備用
const downloadExcelOld = () => {
  if (batchResults.value.length === 0) {
    Notify.create({
      type: 'warning',
      message: '沒有可下載的結果'
    });
    return;
  }

  try {
    // 創建工作簿
    const workbook = XLSX.utils.book_new();

    // 根據分析方法創建不同的工作表
    switch (selectedMethod.value) {
      case 'ball-follow':
        createParametersSheet(workbook, 'ball');
        createPredictNumbersSheet(workbook);
        createNonAppearedByFrequencySheet(workbook);
        createNonAppearedBySizeSheet(workbook);
        createTailNumbersSheet(workbook);
        createActualNumbersSheet(workbook);
        break;
      case 'tail':
        createParametersSheet(workbook, 'tail');
        createTailAppearancesSheet(workbook);
        createActualNumbersSheet(workbook);
        break;
      case 'pattern':
        createParametersSheet(workbook, 'pattern');
        createPatternSheet(workbook);
        break;
    }

    // 生成檔案名稱
    const methodName = getMethodName(selectedMethod.value);
    const lottoName = getLottoTypeName(selectedLottoType.value);
    const dateStr = new Date().toISOString().split('T')[0].replace(/-/g, '');
    const fileName = `${methodName}_${lottoName}_${dateStr}.xlsx`;

    // 下載檔案
    XLSX.writeFile(workbook, fileName);

    Notify.create({
      type: 'positive',
      message: '報表下載成功！'
    });
  } catch (error) {
    console.error('Excel生成失敗:', error);
    Notify.create({
      type: 'negative',
      message: 'Excel檔案生成失敗'
    });
  }
};

// 新的 ExcelJS 版本（支援背景色標記）
const downloadExcel = async () => {
  if (batchResults.value.length === 0) {
    Notify.create({
      type: 'warning',
      message: '沒有可下載的結果'
    });
    return;
  }

  try {
    // 創建工作簿
    const workbook = new ExcelJS.Workbook();
    workbook.creator = '彩票分析系統';
    workbook.created = new Date();

    // 根據分析方法創建不同的工作表
    switch (selectedMethod.value) {
      case 'ball-follow':
        await createParametersSheetExcelJS(workbook, 'ball');
        await createPredictNumbersSheetExcelJS(workbook);
        await createNonAppearedByFrequencySheetExcelJS(workbook);
        await createNonAppearedBySizeSheetExcelJS(workbook);
        await createTailNumbersSheetExcelJS(workbook);
        await createActualNumbersSheetExcelJS(workbook);
        break;
      case 'tail':
        await createParametersSheetExcelJS(workbook, 'tail');
        await createTailAppearancesSheetExcelJS(workbook);
        await createActualNumbersSheetExcelJS(workbook);
        break;
      case 'pattern':
        await createParametersSheetExcelJS(workbook, 'pattern');
        await createPatternSheetExcelJS(workbook);
        break;
    }

    // 生成檔案名稱
    const methodName = getMethodName(selectedMethod.value);
    const lottoName = getLottoTypeName(selectedLottoType.value);
    const dateStr = new Date().toISOString().split('T')[0].replace(/-/g, '');
    const fileName = `${methodName}_${lottoName}_${dateStr}.xlsx`;

    // 下載檔案
    const buffer = await workbook.xlsx.writeBuffer();
    const blob = new Blob([buffer], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = fileName;
    link.click();
    window.URL.revokeObjectURL(url);

    Notify.create({
      type: 'positive',
      message: '報表下載成功！'
    });
  } catch (error) {
    console.error('Excel生成失敗:', error);
    console.error('錯誤詳情:', error);

    // 如果 ExcelJS 失敗，回退到原有的 XLSX 版本
    Notify.create({
      type: 'warning',
      message: '增強版生成失敗，使用標準版本...'
    });
    downloadExcelOld();
  }
};

// ExcelJS 版本的工作表創建函數
// 創建參數工作表（ExcelJS版本）
const createParametersSheetExcelJS = async (workbook: ExcelJS.Workbook, type: 'ball' | 'tail' | 'pattern') => {
  const worksheet = workbook.addWorksheet('分析參數');

  const sheetData: (string | number)[][] = [];

  switch (type) {
    case 'ball':
      // 版路分析參數
      sheetData.push(['版路分析參數']);
      sheetData.push(['分析數量', analysisParams.value.batchAnalysisRange]);
      sheetData.push(['拖牌組合', `${analysisParams.value.ballComb1}-${analysisParams.value.ballComb2}-${analysisParams.value.ballComb3}`]);
      sheetData.push(['推算期數', analysisParams.value.periodNum]);
      sheetData.push(['最大區間', analysisParams.value.maxRange]);
      sheetData.push(['預測期數', analysisParams.value.aheadNum]);
      sheetData.push([]);

      // 篩選條件
      sheetData.push(['篩選條件']);
      const ballAccuracyLabel = accuracyOpts.value.find(opt => opt.value === filterParams.value.ballAccuracy)?.label || filterParams.value.ballAccuracy;
      const ballFilterLabel = filterConditionOpts.value.find(opt => opt.value === filterParams.value.ballFilterCondition)?.label || filterParams.value.ballFilterCondition;
      sheetData.push(['連續拖出次數', ballAccuracyLabel]);
      sheetData.push(['篩選條件', ballFilterLabel]);
      break;

    case 'tail':
      // 尾數分析參數
      sheetData.push(['尾數分析參數']);
      sheetData.push(['分析數量', analysisParams.value.batchAnalysisRange]);
      sheetData.push(['拖牌組合', `${analysisParams.value.tailComb1}-${analysisParams.value.tailComb2}-${analysisParams.value.tailComb3}`]);
      sheetData.push(['推算期數', analysisParams.value.periodNum]);
      sheetData.push(['最大區間', analysisParams.value.maxRange]);
      sheetData.push(['預測期數', analysisParams.value.aheadNum]);
      sheetData.push([]);

      // 篩選條件
      sheetData.push(['篩選條件']);
      const tailAccuracyLabel = tailAccuracyOpts.value.find(opt => opt.value === filterParams.value.tailAccuracy)?.label || filterParams.value.tailAccuracy;
      const tailFilterLabel = filterConditionOpts.value.find(opt => opt.value === filterParams.value.tailFilterCondition)?.label || filterParams.value.tailFilterCondition;
      sheetData.push(['連續拖出次數', tailAccuracyLabel]);
      sheetData.push(['篩選條件', tailFilterLabel]);
      break;

    case 'pattern':
      // 綜合分析參數
      sheetData.push(['綜合分析參數']);
      sheetData.push(['分析數量', analysisParams.value.batchAnalysisRange]);
      sheetData.push([]);

      sheetData.push(['版路分析參數']);
      sheetData.push(['拖牌組合', `${analysisParams.value.ballComb1}-${analysisParams.value.ballComb2}-${analysisParams.value.ballComb3}`]);
      sheetData.push(['推算期數', analysisParams.value.periodNum]);
      sheetData.push(['最大區間', analysisParams.value.maxRange]);
      sheetData.push([]);

      sheetData.push(['尾數分析參數']);
      sheetData.push(['拖牌組合', `${analysisParams.value.tailComb1}-${analysisParams.value.tailComb2}-${analysisParams.value.tailComb3}`]);
      sheetData.push(['推算期數', analysisParams.value.periodNum]);
      sheetData.push(['最大區間', analysisParams.value.maxRange]);
      sheetData.push([]);

      sheetData.push(['預測期數', analysisParams.value.aheadNum]);
      sheetData.push([]);

      // 版路分析篩選條件
      sheetData.push(['版路分析篩選條件']);
      const patternBallAccuracyLabel = accuracyOpts.value.find(opt => opt.value === filterParams.value.ballAccuracy)?.label || filterParams.value.ballAccuracy;
      const patternBallFilterLabel = filterConditionOpts.value.find(opt => opt.value === filterParams.value.ballFilterCondition)?.label || filterParams.value.ballFilterCondition;
      sheetData.push(['連續拖出次數', patternBallAccuracyLabel]);
      sheetData.push(['篩選條件', patternBallFilterLabel]);
      sheetData.push([]);

      // 尾數分析篩選條件
      sheetData.push(['尾數分析篩選條件']);
      const patternTailAccuracyLabel = tailAccuracyOpts.value.find(opt => opt.value === filterParams.value.tailAccuracy)?.label || filterParams.value.tailAccuracy;
      const patternTailFilterLabel = filterConditionOpts.value.find(opt => opt.value === filterParams.value.tailFilterCondition)?.label || filterParams.value.tailFilterCondition;
      sheetData.push(['連續拖出次數', patternTailAccuracyLabel]);
      sheetData.push(['篩選條件', patternTailFilterLabel]);
      break;
  }

  sheetData.push([]);

  // 分析時間
  sheetData.push(['分析資訊']);
  sheetData.push(['分析時間', new Date().toLocaleString('zh-TW')]);
  sheetData.push(['分析期數', batchResults.value.length]);

  // 添加數據到工作表
  sheetData.forEach((row, ) => {
    const excelRow = worksheet.addRow(row);

    // 設置標題行樣式（16號字體）
    if (row.length === 1 && row[0] && typeof row[0] === 'string' &&
        (row[0].includes('參數') || row[0].includes('條件') || row[0].includes('資訊'))) {
      excelRow.font = { bold: true, size: 16 };
    } else {
      // 設置普通行字體為14號
      excelRow.font = { size: 14 };
    }
  });

  // 設置列寬
  worksheet.getColumn(1).width = 20;
  worksheet.getColumn(2).width = 30;
};

// 創建預測號碼統計工作表（ExcelJS版本）
const createPredictNumbersSheetExcelJS = async (workbook: ExcelJS.Workbook) => {
  const worksheet = workbook.addWorksheet('預測號碼');

  // 添加標題
  worksheet.addRow(['預測號碼統計']);
  worksheet.addRow([]);
  const headerRow = worksheet.addRow(['分析日期', '預測日期']);

  // 設置標題樣式（14號字體）
  worksheet.getRow(1).font = { bold: true, size: 16 };
  headerRow.font = { bold: true, size: 14 };

  // 為每期分析結果創建一行
  batchResults.value.forEach(result => {
    if (result.targetNumAppearances) {
      // 按出現次數排序預測號碼
      const sortedNumbers = Array.from(result.targetNumAppearances.entries())
        .sort((a, b) => b[1] - a[1])
        .map(([num]) => num);

      // 每10個號碼一列
      const rows = formatNumbersIntoRows(sortedNumbers);
      const predictDate = getPredictDate(result);

      rows.forEach((row, index) => {
        const rowData = index === 0 ? [result.date, predictDate, ...row] : ['', '', ...row];
        const excelRow = worksheet.addRow(rowData);

        // 設置數據行字體大小為14號
        excelRow.font = { size: 14 };

        // 檢查並標記匹配的號碼（使用背景色）
        if (result.actualNumbers) {
          row.forEach((number, colIndex) => {
            if (number && result.actualNumbers && result.actualNumbers.includes(parseInt(number.toString()))) {
              const cellIndex = colIndex + 3; // 跳過日期欄位
              const cell = excelRow.getCell(cellIndex);

              // 設置紅色背景和白色字體，14號字體
              try {
                cell.fill = {
                  type: 'pattern',
                  pattern: 'solid',
                  fgColor: { argb: 'FFFF0000' }
                };
                cell.font = {
                  color: { argb: 'FFFFFFFF' },
                  bold: true,
                  size: 14
                };
              } catch (error) {
                console.warn('設置儲存格樣式失敗:', error);
              }
            }
          });
        }
      });
    }
  });

  // 設置列寬（調整日期欄位寬度以適應14號字體）
  worksheet.getColumn(1).width = 16; // 分析日期
  worksheet.getColumn(2).width = 16; // 預測日期
  for (let i = 3; i <= 12; i++) {
    worksheet.getColumn(i).width = 8; // 號碼欄
  }
};

// 創建其他必要的 ExcelJS 函數
const createNonAppearedByFrequencySheetExcelJS = async (workbook: ExcelJS.Workbook) => {
  const worksheet = workbook.addWorksheet('未出現號碼-預測次數');

  // 添加標題
  worksheet.addRow(['未出現號碼 - 依預測次數排序']);
  worksheet.addRow([]);
  const headerRow = worksheet.addRow(['分析日期', '預測日期']);

  // 設置標題樣式（14號字體）
  worksheet.getRow(1).font = { bold: true, size: 16 };
  headerRow.font = { bold: true, size: 14 };

  batchResults.value.forEach(result => {
    if (result.nonAppearedByFrequency) {
      // 依預測次數排序
      const sortedByFreq = Array.from(result.nonAppearedByFrequency.entries())
        .sort((a, b) => b[1] - a[1])
        .map(([num]) => num);

      // 每10個號碼一列
      const rows = formatNumbersIntoRows(sortedByFreq);
      const predictDate = getPredictDate(result);

      rows.forEach((row, index) => {
        const rowData = index === 0 ? [result.date, predictDate, ...row] : ['', '', ...row];
        const excelRow = worksheet.addRow(rowData);

        // 設置數據行字體大小為14號
        excelRow.font = { size: 14 };

        // 檢查並標記匹配的號碼
        if (result.actualNumbers) {
          row.forEach((number, colIndex) => {
            if (number && result.actualNumbers && result.actualNumbers.includes(parseInt(number.toString()))) {
              const cellIndex = colIndex + 3; // 跳過日期欄位
              const cell = excelRow.getCell(cellIndex);
              // eslint-disable-next-line @typescript-eslint/no-explicit-any
              (cell as any).fill = {
                type: 'pattern',
                pattern: 'solid',
                fgColor: { argb: 'FFFF0000' } // 紅色背景
              };
              // eslint-disable-next-line @typescript-eslint/no-explicit-any
              (cell as any).font = { color: { argb: 'FFFFFFFF' }, bold: true, size: 14 }; // 白色字體，14號
            }
          });
        }
      });
    }
  });

  // 設置列寬
  worksheet.getColumn(1).width = 16; // 分析日期
  worksheet.getColumn(2).width = 16; // 預測日期
  for (let i = 3; i <= 12; i++) {
    worksheet.getColumn(i).width = 8; // 號碼欄
  }
};

const createNonAppearedBySizeSheetExcelJS = async (workbook: ExcelJS.Workbook) => {
  const worksheet = workbook.addWorksheet('未出現號碼-大小排序');

  // 添加標題
  worksheet.addRow(['未出現號碼 - 依大小排序']);
  worksheet.addRow([]);
  const headerRow = worksheet.addRow(['分析日期', '預測日期']);

  // 設置標題樣式（14號字體）
  worksheet.getRow(1).font = { bold: true, size: 16 };
  headerRow.font = { bold: true, size: 14 };

  batchResults.value.forEach(result => {
    if (result.nonAppearedNumbers) {
      // 依大小排序
      const sortedBySize = [...result.nonAppearedNumbers].sort((a, b) => a - b);

      // 每10個號碼一列
      const rows = formatNumbersIntoRows(sortedBySize);
      const predictDate = getPredictDate(result);

      rows.forEach((row, index) => {
        const rowData = index === 0 ? [result.date, predictDate, ...row] : ['', '', ...row];
        const excelRow = worksheet.addRow(rowData);

        // 設置數據行字體大小為14號
        excelRow.font = { size: 14 };

        // 檢查並標記匹配的號碼
        if (result.actualNumbers) {
          row.forEach((number, colIndex) => {
            if (number && result.actualNumbers && result.actualNumbers.includes(parseInt(number.toString()))) {
              const cellIndex = colIndex + 3; // 跳過日期欄位
              const cell = excelRow.getCell(cellIndex);

              try {
                cell.fill = {
                  type: 'pattern',
                  pattern: 'solid',
                  fgColor: { argb: 'FFFF0000' }
                };
                cell.font = {
                  color: { argb: 'FFFFFFFF' },
                  bold: true,
                  size: 14
                };
              } catch (error) {
                console.warn('設置儲存格樣式失敗:', error);
              }
            }
          });
        }
      });
    }
  });

  // 設置列寬
  worksheet.getColumn(1).width = 16; // 分析日期
  worksheet.getColumn(2).width = 16; // 預測日期
  for (let i = 3; i <= 12; i++) {
    worksheet.getColumn(i).width = 8; // 號碼欄
  }
};

const createTailNumbersSheetExcelJS = async (workbook: ExcelJS.Workbook) => {
  const worksheet = workbook.addWorksheet('尾數統計');

  // 添加標題
  worksheet.addRow(['預測尾數統計']);
  worksheet.addRow([]);
  const headerRow = worksheet.addRow(['分析日期', '預測日期']);

  // 設置標題樣式（14號字體）
  worksheet.getRow(1).font = { bold: true, size: 16 };
  headerRow.font = { bold: true, size: 14 };

  batchResults.value.forEach(result => {
    if (result.tailNumAppearances) {
      // 按統計次數排序（次數高的在前）
      const sortedTails = Array.from(result.tailNumAppearances.entries())
        .sort((a, b) => b[1] - a[1])  // 按次數降序排列
        .map(([tail]) => tail);       // 只取尾數值

      // 每10個尾數一列（尾數不使用padding zero）
      const rows = formatNumbersIntoRows(sortedTails, true);
      const predictDate = getPredictDate(result);

      rows.forEach((row, index) => {
        const rowData = index === 0 ? [result.date, predictDate, ...row] : ['', '', ...row];
        const excelRow = worksheet.addRow(rowData);

        // 設置數據行字體大小為14號
        excelRow.font = { size: 14 };

        // 檢查並標記匹配的尾數
        if (result.actualNumbers) {
          const actualTails = [...new Set(result.actualNumbers.map(n => n % 10))];
          row.forEach((tail, colIndex) => {
            if (tail !== undefined && actualTails.includes(parseInt(tail.toString()))) {
              const cellIndex = colIndex + 3; // 跳過日期欄位
              const cell = excelRow.getCell(cellIndex);

              try {
                cell.fill = {
                  type: 'pattern',
                  pattern: 'solid',
                  fgColor: { argb: 'FFFF0000' }
                };
                cell.font = {
                  color: { argb: 'FFFFFFFF' },
                  bold: true,
                  size: 14
                };
              } catch (error) {
                console.warn('設置儲存格樣式失敗:', error);
              }
            }
          });
        }
      });
    }
  });

  // 設置列寬（調整日期欄位寬度以適應14號字體）
  worksheet.getColumn(1).width = 16; // 分析日期
  worksheet.getColumn(2).width = 16; // 預測日期
  for (let i = 3; i <= 12; i++) {
    worksheet.getColumn(i).width = 6; // 尾數欄
  }
};

const createTailAppearancesSheetExcelJS = async (workbook: ExcelJS.Workbook) => {
  const worksheet = workbook.addWorksheet('預測尾數');

  // 添加標題
  worksheet.addRow(['預測尾數統計']);
  worksheet.addRow([]);
  const headerRow = worksheet.addRow(['分析日期', '預測日期']);

  // 設置標題樣式（14號字體）
  worksheet.getRow(1).font = { bold: true, size: 16 };
  headerRow.font = { bold: true, size: 14 };

  // 為每期分析結果創建一行
  batchResults.value.forEach(result => {
    if (result.tailNumAppearances) {
      // 按出現次數排序尾數
      const sortedTails = Array.from(result.tailNumAppearances.entries())
        .sort((a, b) => b[1] - a[1])
        .map(([tail]) => tail);

      // 每10個尾數一列（尾數不使用padding zero）
      const rows = formatNumbersIntoRows(sortedTails, true);
      const predictDate = getPredictDate(result);

      rows.forEach((row, index) => {
        const rowData = index === 0 ? [result.date, predictDate, ...row] : ['', '', ...row];
        const excelRow = worksheet.addRow(rowData);

        // 設置數據行字體大小為14號
        excelRow.font = { size: 14 };

        // 檢查並標記匹配的尾數
        if (result.actualNumbers) {
          const actualTails = [...new Set(result.actualNumbers.map(n => n % 10))];
          row.forEach((tail, colIndex) => {
            if (tail !== undefined && actualTails.includes(parseInt(tail.toString()))) {
              const cellIndex = colIndex + 3; // 跳過日期欄位
              const cell = excelRow.getCell(cellIndex);

              try {
                cell.fill = {
                  type: 'pattern',
                  pattern: 'solid',
                  fgColor: { argb: 'FFFF0000' }
                };
                cell.font = {
                  color: { argb: 'FFFFFFFF' },
                  bold: true,
                  size: 14
                };
              } catch (error) {
                console.warn('設置儲存格樣式失敗:', error);
              }
            }
          });
        }
      });
    }
  });

  // 設置列寬（調整日期欄位寬度以適應14號字體）
  worksheet.getColumn(1).width = 16; // 分析日期
  worksheet.getColumn(2).width = 16; // 預測日期
  for (let i = 3; i <= 12; i++) {
    worksheet.getColumn(i).width = 6; // 尾數欄
  }
};

const createActualNumbersSheetExcelJS = async (workbook: ExcelJS.Workbook) => {
  const worksheet = workbook.addWorksheet('實際開獎號碼');

  // 獲取預測參數
  const params = getAnalysisParameters();
  const aheadCount = params.aheadNum;

  worksheet.addRow([`實際開獎號碼 (預測${aheadCount}期後)`]);
  worksheet.addRow([]);

  // 檢查實際數據中是否有特別號
  const hasSpecialNumber = batchResults.value.some(result => {
    if (!result.actualNumbers || result.actualNumbers.length === 0) return false;

    // 根據彩種判斷是否應該有特別號
    if (selectedLottoType.value === 'super_lotto638') {
      return false; // 威力彩沒有特別號
    } else if (selectedLottoType.value === 'daily539') {
      return false; // 今彩539沒有特別號
    } else {
      // 大樂透和六合彩有特別號，檢查實際數據長度
      return result.actualNumbers.length > 6;
    }
  });

  // 創建表頭
  const headers = ['分析日期', '預測日期'];

  // 根據彩種決定號碼欄位數量
  let maxNumbers = 6; // 預設6個號碼
  if (selectedLottoType.value === 'super_lotto638') {
    maxNumbers = 6; // 威力彩6個號碼
  } else if (selectedLottoType.value === 'lotto649') {
    maxNumbers = 6; // 大樂透6個號碼
  } else if (selectedLottoType.value === 'daily539') {
    maxNumbers = 5; // 今彩539只有5個號碼
  } else if (selectedLottoType.value === 'lotto_hk') {
    maxNumbers = 6; // 六合彩6個號碼
  }

  // 添加一般號碼欄位
  for (let i = 1; i <= maxNumbers; i++) {
    headers.push(`號碼${i}`);
  }

  // 只有在實際有特別號的情況下才添加特別號欄位
  if (hasSpecialNumber) {
    headers.push('特別號');
  }

  const headerRow = worksheet.addRow(headers);
  headerRow.font = { bold: true, size: 14 };

  // 設置標題樣式
  worksheet.getRow(1).font = { bold: true, size: 16 };

  // 添加數據行
  batchResults.value.forEach(result => {
    // 從predictResponse中獲取正確的預測日期
    let predictDate = '';
    let isDrawn = false;

    if (result.predictResponse) {
      if (result.predictResponse.draw_date) {
        predictDate = result.predictResponse.draw_date;
        // 檢查是否已開獎（有期號表示已開獎）
        isDrawn = !!result.predictResponse.period;
      } else {
        predictDate = '尚未開獎';
      }
    } else {
      predictDate = '尚未開獎';
    }

    const row: (string | number)[] = [result.date, predictDate];

    // 簡化實現：直接顯示所有實際號碼
    if (result.actualNumbers && result.actualNumbers.length > 0 && isDrawn) {
      const sortedNumbers = [...result.actualNumbers].sort((a, b) => a - b);
      sortedNumbers.forEach(num => {
        row.push(num.toString().padStart(2, '0'));
      });
    } else {
      // 填充空白或未開獎
      for (let i = 0; i < maxNumbers + (hasSpecialNumber ? 1 : 0); i++) {
        if (!isDrawn && predictDate !== '尚未開獎') {
          row.push('尚未開獎');
        } else {
          row.push('');
        }
      }
    }

    const excelRow = worksheet.addRow(row);
    // 設置數據行字體大小為14號
    excelRow.font = { size: 14 };
  });

  // 設置列寬（調整日期欄位寬度以適應14號字體）
  worksheet.getColumn(1).width = 16; // 分析日期
  worksheet.getColumn(2).width = 16; // 預測日期
  for (let i = 0; i < maxNumbers + (hasSpecialNumber ? 1 : 0); i++) {
    worksheet.getColumn(i + 3).width = 8; // 號碼欄
  }
};

const createPatternSheetExcelJS = async (workbook: ExcelJS.Workbook) => {
  // 根據原有的 createPatternSheet 實現，創建所有綜合分析的工作表

  // 創建綜合分析專用工作表（放在最前面）
  await createPatternRdTailAnalysisSheetExcelJS(workbook);
  await createPatternTailRdAnalysisSheetExcelJS(workbook);
  await createPatternComprehensiveAnalysisSheetExcelJS(workbook);
  await createPatternRdTailComparisonSheetExcelJS(workbook);
  await createPatternRdComprehensiveComparisonSheetExcelJS(workbook);
  await createPatternTailComprehensiveComparisonSheetExcelJS(workbook);

  // 創建版路分析子表（添加標記）
  await createPatternPredictNumbersSheetExcelJS(workbook);
  await createPatternNonAppearedByFrequencySheetExcelJS(workbook);
  await createPatternNonAppearedBySizeSheetExcelJS(workbook);
  await createPatternTailNumbersSheetExcelJS(workbook);

  // 創建尾數分析子表（添加標記）
  await createPatternTailAppearancesSheetExcelJS(workbook);

  // 創建實際開獎號碼工作表
  await createActualNumbersSheetExcelJS(workbook);
};

// 綜合分析專用工作表的 ExcelJS 函數
const createPatternRdTailAnalysisSheetExcelJS = async (workbook: ExcelJS.Workbook) => {
  const worksheet = workbook.addWorksheet('版路分析尾數');

  worksheet.addRow(['版路分析尾數']);
  worksheet.addRow([]);
  const headerRow = worksheet.addRow(['分析日期', '預測日期']);

  // 設置標題樣式（14號字體）
  worksheet.getRow(1).font = { bold: true, size: 16 };
  headerRow.font = { bold: true, size: 14 };

  batchResults.value.forEach(result => {
    if (result.rdTailAppearances) {
      const sortedTails = Array.from(result.rdTailAppearances.entries())
        .sort((a, b) => b[1] - a[1])
        .map(([tail]) => tail);

      const rows = formatNumbersIntoRows(sortedTails, true);
      const predictDate = getPredictDate(result);

      rows.forEach((row, index) => {
        const rowData = index === 0 ? [result.date, predictDate, ...row] : ['', '', ...row];
        const excelRow = worksheet.addRow(rowData);

        // 設置數據行字體大小為14號
        excelRow.font = { size: 14 };

        // 檢查並標記匹配的尾數
        if (result.actualNumbers) {
          const actualTails = [...new Set(result.actualNumbers.map(n => n % 10))];
          row.forEach((tail, colIndex) => {
            if (tail !== undefined && actualTails.includes(parseInt(tail.toString()))) {
              const cellIndex = colIndex + 3;
              const cell = excelRow.getCell(cellIndex);

              try {
                cell.fill = {
                  type: 'pattern',
                  pattern: 'solid',
                  fgColor: { argb: 'FFFF0000' }
                };
                cell.font = {
                  color: { argb: 'FFFFFFFF' },
                  bold: true,
                  size: 14
                };
              } catch (error) {
                console.warn('設置儲存格樣式失敗:', error);
              }
            }
          });
        }
      });
    }
  });

  worksheet.getColumn(1).width = 16;
  worksheet.getColumn(2).width = 16;
  for (let i = 3; i <= 12; i++) {
    worksheet.getColumn(i).width = 6;
  }
};

const createPatternTailRdAnalysisSheetExcelJS = async (workbook: ExcelJS.Workbook) => {
  const worksheet = workbook.addWorksheet('尾數分析尾數');

  worksheet.addRow(['尾數分析尾數']);
  worksheet.addRow([]);
  const headerRow = worksheet.addRow(['分析日期', '預測日期']);

  worksheet.getRow(1).font = { bold: true, size: 16 };
  headerRow.font = { bold: true, size: 14 };

  batchResults.value.forEach(result => {
    if (result.tailRdScoreMap) {
      const sortedTails = Array.from(result.tailRdScoreMap.entries())
        .sort((a, b) => b[1] - a[1])
        .map(([tail]) => tail);

      const rows = formatNumbersIntoRows(sortedTails, true);
      const predictDate = getPredictDate(result);

      rows.forEach((row, index) => {
        const rowData = index === 0 ? [result.date, predictDate, ...row] : ['', '', ...row];
        const excelRow = worksheet.addRow(rowData);
        excelRow.font = { size: 14 };

        // 檢查並標記匹配的尾數
        if (result.actualNumbers) {
          const actualTails = [...new Set(result.actualNumbers.map(n => n % 10))];
          row.forEach((tail, colIndex) => {
            if (tail !== undefined && actualTails.includes(parseInt(tail.toString()))) {
              const cellIndex = colIndex + 3; // 跳過日期欄位
              const cell = excelRow.getCell(cellIndex);

              try {
                cell.fill = {
                  type: 'pattern',
                  pattern: 'solid',
                  fgColor: { argb: 'FFFF0000' }
                };
                cell.font = {
                  color: { argb: 'FFFFFFFF' },
                  bold: true,
                  size: 14
                };
              } catch (error) {
                console.warn('設置儲存格樣式失敗:', error);
              }
            }
          });
        }
      });
    }
  });

  worksheet.getColumn(1).width = 16;
  worksheet.getColumn(2).width = 16;
  for (let i = 3; i <= 12; i++) {
    worksheet.getColumn(i).width = 6;
  }
};

const createPatternComprehensiveAnalysisSheetExcelJS = async (workbook: ExcelJS.Workbook) => {
  const worksheet = workbook.addWorksheet('綜合分析尾數');

  worksheet.addRow(['綜合分析尾數']);
  worksheet.addRow([]);
  const headerRow = worksheet.addRow(['分析日期', '預測日期']);

  worksheet.getRow(1).font = { bold: true, size: 16 };
  headerRow.font = { bold: true, size: 14 };

  batchResults.value.forEach(result => {
    if (result.tailScoreMap) {
      const sortedTails = Array.from(result.tailScoreMap.entries())
        .sort((a, b) => b[1] - a[1])
        .map(([tail]) => tail);

      const rows = formatNumbersIntoRows(sortedTails, true);
      const predictDate = getPredictDate(result);

      rows.forEach((row, index) => {
        const rowData = index === 0 ? [result.date, predictDate, ...row] : ['', '', ...row];
        const excelRow = worksheet.addRow(rowData);
        excelRow.font = { size: 14 };

        // 檢查並標記匹配的尾數
        if (result.actualNumbers) {
          const actualTails = [...new Set(result.actualNumbers.map(n => n % 10))];
          row.forEach((tail, colIndex) => {
            if (tail !== undefined && actualTails.includes(parseInt(tail.toString()))) {
              const cellIndex = colIndex + 3; // 跳過日期欄位
              const cell = excelRow.getCell(cellIndex);

              try {
                cell.fill = {
                  type: 'pattern',
                  pattern: 'solid',
                  fgColor: { argb: 'FFFF0000' }
                };
                cell.font = {
                  color: { argb: 'FFFFFFFF' },
                  bold: true,
                  size: 14
                };
              } catch (error) {
                console.warn('設置儲存格樣式失敗:', error);
              }
            }
          });
        }
      });
    }
  });

  worksheet.getColumn(1).width = 16;
  worksheet.getColumn(2).width = 16;
  for (let i = 3; i <= 12; i++) {
    worksheet.getColumn(i).width = 6;
  }
};

// 比對工作表的 ExcelJS 函數
const createPatternRdTailComparisonSheetExcelJS = async (workbook: ExcelJS.Workbook) => {
  const worksheet = workbook.addWorksheet('版路+尾數比對');

  worksheet.addRow(['版路+尾數比對']);
  worksheet.addRow([]);
  const headerRow = worksheet.addRow(['分析日期', '預測日期']);

  worksheet.getRow(1).font = { bold: true, size: 16 };
  headerRow.font = { bold: true, size: 14 };

  batchResults.value.forEach(result => {
    if (result.tailMatchResults) {
      const rows = formatNumbersIntoRows(result.tailMatchResults, true);
      const predictDate = getPredictDate(result);

      rows.forEach((row, index) => {
        const rowData = index === 0 ? [result.date, predictDate, ...row] : ['', '', ...row];
        const excelRow = worksheet.addRow(rowData);
        excelRow.font = { size: 14 };

        // 檢查並標記匹配的尾數
        if (result.actualNumbers) {
          const actualTails = [...new Set(result.actualNumbers.map(n => n % 10))];
          row.forEach((tail, colIndex) => {
            if (tail !== undefined && actualTails.includes(parseInt(tail.toString()))) {
              const cellIndex = colIndex + 3; // 跳過日期欄位
              const cell = excelRow.getCell(cellIndex);

              try {
                cell.fill = {
                  type: 'pattern',
                  pattern: 'solid',
                  fgColor: { argb: 'FFFF0000' }
                };
                cell.font = {
                  color: { argb: 'FFFFFFFF' },
                  bold: true,
                  size: 14
                };
              } catch (error) {
                console.warn('設置儲存格樣式失敗:', error);
              }
            }
          });
        }
      });
    }
  });

  worksheet.getColumn(1).width = 16;
  worksheet.getColumn(2).width = 16;
  for (let i = 3; i <= 12; i++) {
    worksheet.getColumn(i).width = 6;
  }
};

const createPatternRdComprehensiveComparisonSheetExcelJS = async (workbook: ExcelJS.Workbook) => {
  const worksheet = workbook.addWorksheet('版路+綜合比對');

  worksheet.addRow(['版路+綜合比對']);
  worksheet.addRow([]);
  const headerRow = worksheet.addRow(['分析日期', '預測日期']);

  worksheet.getRow(1).font = { bold: true, size: 16 };
  headerRow.font = { bold: true, size: 14 };

  batchResults.value.forEach(result => {
    if (result.tailMatchResults2) {
      const rows = formatNumbersIntoRows(result.tailMatchResults2, true);
      const predictDate = getPredictDate(result);

      rows.forEach((row, index) => {
        const rowData = index === 0 ? [result.date, predictDate, ...row] : ['', '', ...row];
        const excelRow = worksheet.addRow(rowData);
        excelRow.font = { size: 14 };

        // 檢查並標記匹配的尾數
        if (result.actualNumbers) {
          const actualTails = [...new Set(result.actualNumbers.map(n => n % 10))];
          row.forEach((tail, colIndex) => {
            if (tail !== undefined && actualTails.includes(parseInt(tail.toString()))) {
              const cellIndex = colIndex + 3; // 跳過日期欄位
              const cell = excelRow.getCell(cellIndex);

              try {
                cell.fill = {
                  type: 'pattern',
                  pattern: 'solid',
                  fgColor: { argb: 'FFFF0000' }
                };
                cell.font = {
                  color: { argb: 'FFFFFFFF' },
                  bold: true,
                  size: 14
                };
              } catch (error) {
                console.warn('設置儲存格樣式失敗:', error);
              }
            }
          });
        }
      });
    }
  });

  worksheet.getColumn(1).width = 16;
  worksheet.getColumn(2).width = 16;
  for (let i = 3; i <= 12; i++) {
    worksheet.getColumn(i).width = 6;
  }
};

const createPatternTailComprehensiveComparisonSheetExcelJS = async (workbook: ExcelJS.Workbook) => {
  const worksheet = workbook.addWorksheet('尾數+綜合比對');

  worksheet.addRow(['尾數+綜合比對']);
  worksheet.addRow([]);
  const headerRow = worksheet.addRow(['分析日期', '預測日期']);

  worksheet.getRow(1).font = { bold: true, size: 16 };
  headerRow.font = { bold: true, size: 14 };

  batchResults.value.forEach(result => {
    if (result.tailMatchResults3) {
      const rows = formatNumbersIntoRows(result.tailMatchResults3, true);
      const predictDate = getPredictDate(result);

      rows.forEach((row, index) => {
        const rowData = index === 0 ? [result.date, predictDate, ...row] : ['', '', ...row];
        const excelRow = worksheet.addRow(rowData);
        excelRow.font = { size: 14 };

        // 檢查並標記匹配的尾數
        if (result.actualNumbers) {
          const actualTails = [...new Set(result.actualNumbers.map(n => n % 10))];
          row.forEach((tail, colIndex) => {
            if (tail !== undefined && actualTails.includes(parseInt(tail.toString()))) {
              const cellIndex = colIndex + 3; // 跳過日期欄位
              const cell = excelRow.getCell(cellIndex);

              try {
                cell.fill = {
                  type: 'pattern',
                  pattern: 'solid',
                  fgColor: { argb: 'FFFF0000' }
                };
                cell.font = {
                  color: { argb: 'FFFFFFFF' },
                  bold: true,
                  size: 14
                };
              } catch (error) {
                console.warn('設置儲存格樣式失敗:', error);
              }
            }
          });
        }
      });
    }
  });

  worksheet.getColumn(1).width = 16;
  worksheet.getColumn(2).width = 16;
  for (let i = 3; i <= 12; i++) {
    worksheet.getColumn(i).width = 6;
  }
};

// 綜合分析的 ExcelJS 函數
const createPatternPredictNumbersSheetExcelJS = async (workbook: ExcelJS.Workbook) => {
  const worksheet = workbook.addWorksheet('版路分析-預測號碼');

  worksheet.addRow(['版路分析-預測號碼統計']);
  worksheet.addRow([]);
  const headerRow = worksheet.addRow(['分析日期', '預測日期']);

  worksheet.getRow(1).font = { bold: true, size: 16 };
  headerRow.font = { bold: true, size: 14 };

  // 使用與 createPredictNumbersSheetExcelJS 相同的邏輯
  batchResults.value.forEach(result => {
    if (result.targetNumAppearances) {
      const sortedNumbers = Array.from(result.targetNumAppearances.entries())
        .sort((a, b) => b[1] - a[1])
        .map(([num]) => num);

      const rows = formatNumbersIntoRows(sortedNumbers);
      const predictDate = getPredictDate(result);

      rows.forEach((row, index) => {
        const rowData = index === 0 ? [result.date, predictDate, ...row] : ['', '', ...row];
        const excelRow = worksheet.addRow(rowData);

        // 設置數據行字體大小為14號
        excelRow.font = { size: 14 };

        if (result.actualNumbers) {
          row.forEach((number, colIndex) => {
            if (number && result.actualNumbers && result.actualNumbers.includes(parseInt(number.toString()))) {
              const cellIndex = colIndex + 3;
              const cell = excelRow.getCell(cellIndex);

              try {
                cell.fill = {
                  type: 'pattern',
                  pattern: 'solid',
                  fgColor: { argb: 'FFFF0000' }
                };
                cell.font = {
                  color: { argb: 'FFFFFFFF' },
                  bold: true,
                  size: 14
                };
              } catch (error) {
                console.warn('設置儲存格樣式失敗:', error);
              }
            }
          });
        }
      });
    }
  });

  worksheet.getColumn(1).width = 16;
  worksheet.getColumn(2).width = 16;
  for (let i = 3; i <= 12; i++) {
    worksheet.getColumn(i).width = 8;
  }
};

const createPatternNonAppearedByFrequencySheetExcelJS = async (workbook: ExcelJS.Workbook) => {
  const worksheet = workbook.addWorksheet('版路分析-未出現號碼-預測次數');

  worksheet.addRow(['版路分析-未出現號碼 - 依預測次數排序']);
  worksheet.addRow([]);
  const headerRow = worksheet.addRow(['分析日期', '預測日期']);

  worksheet.getRow(1).font = { bold: true, size: 16 };
  headerRow.font = { bold: true, size: 14 };

  // 簡化實現，使用基本邏輯
  batchResults.value.forEach(result => {
    if (result.nonAppearedByFrequency) {
      const sortedByFreq = Array.from(result.nonAppearedByFrequency.entries())
        .sort((a, b) => b[1] - a[1])
        .map(([num]) => num);

      const rows = formatNumbersIntoRows(sortedByFreq);
      const predictDate = getPredictDate(result);

      rows.forEach((row, index) => {
        const rowData = index === 0 ? [result.date, predictDate, ...row] : ['', '', ...row];
        const excelRow = worksheet.addRow(rowData);

        // 設置數據行字體大小為14號
        excelRow.font = { size: 14 };

        // 檢查並標記匹配的號碼
        if (result.actualNumbers) {
          row.forEach((number, colIndex) => {
            if (number && result.actualNumbers && result.actualNumbers.includes(parseInt(number.toString()))) {
              const cellIndex = colIndex + 3; // 跳過日期欄位
              const cell = excelRow.getCell(cellIndex);

              try {
                cell.fill = {
                  type: 'pattern',
                  pattern: 'solid',
                  fgColor: { argb: 'FFFF0000' }
                };
                cell.font = {
                  color: { argb: 'FFFFFFFF' },
                  bold: true,
                  size: 14
                };
              } catch (error) {
                console.warn('設置儲存格樣式失敗:', error);
              }
            }
          });
        }
      });
    }
  });

  worksheet.getColumn(1).width = 16;
  worksheet.getColumn(2).width = 16;
  for (let i = 3; i <= 12; i++) {
    worksheet.getColumn(i).width = 8;
  }
};

const createPatternNonAppearedBySizeSheetExcelJS = async (workbook: ExcelJS.Workbook) => {
  const worksheet = workbook.addWorksheet('版路分析-未出現號碼-大小排序');

  worksheet.addRow(['版路分析-未出現號碼 - 依大小排序']);
  worksheet.addRow([]);
  const headerRow = worksheet.addRow(['分析日期', '預測日期']);

  worksheet.getRow(1).font = { bold: true, size: 16 };
  headerRow.font = { bold: true, size: 14 };

  batchResults.value.forEach(result => {
    if (result.nonAppearedNumbers) {
      const sortedBySize = [...result.nonAppearedNumbers].sort((a, b) => a - b);
      const rows = formatNumbersIntoRows(sortedBySize);
      const predictDate = getPredictDate(result);

      rows.forEach((row, index) => {
        const rowData = index === 0 ? [result.date, predictDate, ...row] : ['', '', ...row];
        const excelRow = worksheet.addRow(rowData);
        excelRow.font = { size: 14 };

        // 檢查並標記匹配的號碼
        if (result.actualNumbers) {
          row.forEach((number, colIndex) => {
            if (number && result.actualNumbers && result.actualNumbers.includes(parseInt(number.toString()))) {
              const cellIndex = colIndex + 3; // 跳過日期欄位
              const cell = excelRow.getCell(cellIndex);

              try {
                cell.fill = {
                  type: 'pattern',
                  pattern: 'solid',
                  fgColor: { argb: 'FFFF0000' }
                };
                cell.font = {
                  color: { argb: 'FFFFFFFF' },
                  bold: true,
                  size: 14
                };
              } catch (error) {
                console.warn('設置儲存格樣式失敗:', error);
              }
            }
          });
        }
      });
    }
  });

  worksheet.getColumn(1).width = 16;
  worksheet.getColumn(2).width = 16;
  for (let i = 3; i <= 12; i++) {
    worksheet.getColumn(i).width = 8;
  }
};

const createPatternTailNumbersSheetExcelJS = async (workbook: ExcelJS.Workbook) => {
  const worksheet = workbook.addWorksheet('版路分析-尾數統計');

  worksheet.addRow(['版路分析-尾數統計']);
  worksheet.addRow([]);
  const headerRow = worksheet.addRow(['分析日期', '預測日期']);

  worksheet.getRow(1).font = { bold: true, size: 16 };
  headerRow.font = { bold: true, size: 14 };

  batchResults.value.forEach(result => {
    if (result.tailNumAppearances) {
      const sortedTails = Array.from(result.tailNumAppearances.entries())
        .sort((a, b) => b[1] - a[1])
        .map(([tail]) => tail);

      const rows = formatNumbersIntoRows(sortedTails, true);
      const predictDate = getPredictDate(result);

      rows.forEach((row, index) => {
        const rowData = index === 0 ? [result.date, predictDate, ...row] : ['', '', ...row];
        const excelRow = worksheet.addRow(rowData);
        excelRow.font = { size: 14 };

        // 檢查並標記匹配的尾數
        if (result.actualNumbers) {
          const actualTails = [...new Set(result.actualNumbers.map(n => n % 10))];
          row.forEach((tail, colIndex) => {
            if (tail !== undefined && actualTails.includes(parseInt(tail.toString()))) {
              const cellIndex = colIndex + 3;
              const cell = excelRow.getCell(cellIndex);

              try {
                cell.fill = {
                  type: 'pattern',
                  pattern: 'solid',
                  fgColor: { argb: 'FFFF0000' }
                };
                cell.font = {
                  color: { argb: 'FFFFFFFF' },
                  bold: true,
                  size: 14
                };
              } catch (error) {
                console.warn('設置儲存格樣式失敗:', error);
              }
            }
          });
        }
      });
    }
  });

  worksheet.getColumn(1).width = 16;
  worksheet.getColumn(2).width = 16;
  for (let i = 3; i <= 12; i++) {
    worksheet.getColumn(i).width = 6;
  }
};

const createPatternTailAppearancesSheetExcelJS = async (workbook: ExcelJS.Workbook) => {
  const worksheet = workbook.addWorksheet('尾數分析-預測尾數');

  worksheet.addRow(['尾數分析-預測尾數統計']);
  worksheet.addRow([]);
  const headerRow = worksheet.addRow(['分析日期', '預測日期']);

  worksheet.getRow(1).font = { bold: true, size: 16 };
  headerRow.font = { bold: true, size: 14 };

  batchResults.value.forEach(result => {
    if (result.tailAnalysisAppearances) {
      const sortedTails = Array.from(result.tailAnalysisAppearances.entries())
        .sort((a, b) => b[1] - a[1])
        .map(([tail]) => tail);

      const rows = formatNumbersIntoRows(sortedTails, true);
      const predictDate = getPredictDate(result);

      rows.forEach((row, index) => {
        const rowData = index === 0 ? [result.date, predictDate, ...row] : ['', '', ...row];
        const excelRow = worksheet.addRow(rowData);
        excelRow.font = { size: 14 };

        // 檢查並標記匹配的尾數
        if (result.actualNumbers) {
          const actualTails = [...new Set(result.actualNumbers.map(n => n % 10))];
          row.forEach((tail, colIndex) => {
            if (tail !== undefined && actualTails.includes(parseInt(tail.toString()))) {
              const cellIndex = colIndex + 3;
              const cell = excelRow.getCell(cellIndex);

              try {
                cell.fill = {
                  type: 'pattern',
                  pattern: 'solid',
                  fgColor: { argb: 'FFFF0000' }
                };
                cell.font = {
                  color: { argb: 'FFFFFFFF' },
                  bold: true,
                  size: 14
                };
              } catch (error) {
                console.warn('設置儲存格樣式失敗:', error);
              }
            }
          });
        }
      });
    }
  });

  worksheet.getColumn(1).width = 16;
  worksheet.getColumn(2).width = 16;
  for (let i = 3; i <= 12; i++) {
    worksheet.getColumn(i).width = 6;
  }
};

// 創建預測號碼統計工作表
const createPredictNumbersSheet = (workbook: XLSX.WorkBook) => {
  const sheetData: (string | number)[][] = [];

  sheetData.push(['預測號碼統計']);
  sheetData.push([]);
  sheetData.push(['分析日期', '預測日期']);

  // 為每期分析結果創建一行
  batchResults.value.forEach(result => {
    if (result.targetNumAppearances) {
      // 按出現次數排序預測號碼
      const sortedNumbers = Array.from(result.targetNumAppearances.entries())
        .sort((a, b) => b[1] - a[1])
        .map(([num]) => num);

      // 每10個號碼一列
      const rows = formatNumbersIntoRows(sortedNumbers);
      const predictDate = getPredictDate(result);

      rows.forEach((row, index) => {
        if (index === 0) {
          sheetData.push([result.date, predictDate, ...row]);
        } else {
          sheetData.push(['', '', ...row]); // 空白日期，對齊號碼
        }
      });
    }
  });

  const worksheet = XLSX.utils.aoa_to_sheet(sheetData);

  // 應用紅色字體樣式給與實際開獎號碼相符的預測號碼
  applyRedFontForMatches(worksheet, sheetData, 'predict');

  // 設置列寬
  const colWidths = [
    { wch: 12 }, // 分析日期欄
    { wch: 12 }, // 預測日期欄
    ...Array(10).fill({ wch: 8 }) // 號碼欄
  ];
  worksheet['!cols'] = colWidths;

  XLSX.utils.book_append_sheet(workbook, worksheet, '預測號碼');
};

// 創建未出現號碼工作表 - 依預測次數排序
const createNonAppearedByFrequencySheet = (workbook: XLSX.WorkBook) => {
  const sheetData: (string | number)[][] = [];

  sheetData.push(['未出現號碼 - 依預測次數排序']);
  sheetData.push([]);
  sheetData.push(['分析日期', '預測日期']);

  batchResults.value.forEach(result => {
    if (result.nonAppearedByFrequency) {
      // 按預測次數排序未出現號碼
      const sortedByFreq = Array.from(result.nonAppearedByFrequency.entries())
        .sort((a, b) => b[1] - a[1])
        .map(([num]) => num);

      // 每10個號碼一列
      const rows = formatNumbersIntoRows(sortedByFreq);
      const predictDate = getPredictDate(result);

      rows.forEach((row, index) => {
        if (index === 0) {
          sheetData.push([result.date, predictDate, ...row]);
        } else {
          sheetData.push(['', '', ...row]);
        }
      });
    }
  });

  const worksheet = XLSX.utils.aoa_to_sheet(sheetData);

  // 應用紅色字體樣式（理論上未出現號碼不會有匹配，但為了完整性添加）
  applyRedFontForMatches(worksheet, sheetData, 'predict');

  // 設置列寬
  const colWidths = [
    { wch: 12 }, // 分析日期欄
    { wch: 12 }, // 預測日期欄
    ...Array(10).fill({ wch: 8 }) // 號碼欄
  ];
  worksheet['!cols'] = colWidths;

  XLSX.utils.book_append_sheet(workbook, worksheet, '未出現號碼-預測次數');
};

// 創建未出現號碼工作表 - 依大小排序
const createNonAppearedBySizeSheet = (workbook: XLSX.WorkBook) => {
  const sheetData: (string | number)[][] = [];

  sheetData.push(['未出現號碼 - 依大小排序']);
  sheetData.push([]);
  sheetData.push(['分析日期', '預測日期']);

  batchResults.value.forEach(result => {
    if (result.nonAppearedNumbers) {
      // 依大小排序
      const sortedBySize = [...result.nonAppearedNumbers].sort((a, b) => a - b);

      // 每10個號碼一列
      const rows = formatNumbersIntoRows(sortedBySize);
      const predictDate = getPredictDate(result);

      rows.forEach((row, index) => {
        if (index === 0) {
          sheetData.push([result.date, predictDate, ...row]);
        } else {
          sheetData.push(['', '', ...row]);
        }
      });
    }
  });

  const worksheet = XLSX.utils.aoa_to_sheet(sheetData);

  // 應用紅色字體樣式（理論上未出現號碼不會有匹配，但為了完整性添加）
  applyRedFontForMatches(worksheet, sheetData, 'predict');

  // 設置列寬
  const colWidths = [
    { wch: 12 }, // 分析日期欄
    { wch: 12 }, // 預測日期欄
    ...Array(10).fill({ wch: 8 }) // 號碼欄
  ];
  worksheet['!cols'] = colWidths;

  XLSX.utils.book_append_sheet(workbook, worksheet, '未出現號碼-大小排序');
};

// 創建尾數統計工作表
const createTailNumbersSheet = (workbook: XLSX.WorkBook) => {
  const sheetData: (string | number)[][] = [];

  sheetData.push(['預測尾數統計']);
  sheetData.push([]);
  sheetData.push(['分析日期', '預測日期']);

  batchResults.value.forEach(result => {
    if (result.tailNumAppearances) {
      // 按統計次數排序（次數高的在前）
      const sortedTails = Array.from(result.tailNumAppearances.entries())
        .sort((a, b) => b[1] - a[1])  // 按次數降序排列
        .map(([tail]) => tail);       // 只取尾數值

      // 每10個尾數一列（尾數不使用padding zero）
      const rows = formatNumbersIntoRows(sortedTails, true);
      const predictDate = getPredictDate(result);

      rows.forEach((row, index) => {
        if (index === 0) {
          sheetData.push([result.date, predictDate, ...row]);
        } else {
          sheetData.push(['', '', ...row]);
        }
      });
    }
  });

  const worksheet = XLSX.utils.aoa_to_sheet(sheetData);

  // 應用紅色字體樣式給與實際開獎尾數相符的預測尾數
  applyRedFontForMatches(worksheet, sheetData, 'tail');

  // 設置列寬
  const colWidths = [
    { wch: 12 }, // 分析日期欄
    { wch: 12 }, // 預測日期欄
    ...Array(10).fill({ wch: 6 }) // 尾數欄
  ];
  worksheet['!cols'] = colWidths;

  XLSX.utils.book_append_sheet(workbook, worksheet, '尾數統計');
};

// 創建實際開獎號碼工作表
const createActualNumbersSheet = (workbook: XLSX.WorkBook) => {
  const sheetData: (string | number)[][] = [];

  // 獲取預測參數
  const params = getAnalysisParameters();
  const aheadCount = params.aheadNum;

  sheetData.push([`實際開獎號碼 (預測${aheadCount}期後)`]);
  sheetData.push([]);

  // 檢查實際數據中是否有特別號
  const hasSpecialNumber = batchResults.value.some(result => {
    if (!result.actualNumbers || result.actualNumbers.length === 0) return false;

    // 根據彩種判斷是否應該有特別號
    if (selectedLottoType.value === 'super_lotto638') {
      return false; // 威力彩沒有特別號
    } else if (selectedLottoType.value === 'daily539') {
      return false; // 今彩539沒有特別號
    } else {
      // 大樂透和六合彩有特別號，檢查實際數據長度
      return result.actualNumbers.length > 6;
    }
  });

  // 創建表頭
  const headers = ['分析日期', '預測日期'];

  // 根據彩種決定號碼欄位數量
  let maxNumbers = 6; // 預設6個號碼
  if (selectedLottoType.value === 'super_lotto638') {
    maxNumbers = 6; // 威力彩6個號碼
  } else if (selectedLottoType.value === 'lotto649') {
    maxNumbers = 6; // 大樂透6個號碼
  } else if (selectedLottoType.value === 'daily539') {
    maxNumbers = 5; // 今彩539只有5個號碼
  } else if (selectedLottoType.value === 'lotto_hk') {
    maxNumbers = 6; // 六合彩6個號碼
  }

  // 添加一般號碼欄位
  for (let i = 1; i <= maxNumbers; i++) {
    headers.push(`號碼${i}`);
  }

  // 只有在實際有特別號的情況下才添加特別號欄位
  if (hasSpecialNumber) {
    headers.push('特別號');
  }

  sheetData.push(headers);

  // 添加數據行
  batchResults.value.forEach(result => {
    // 從predictResponse中獲取正確的預測日期
    let predictDate = '';
    let isDrawn = false;

    if (result.predictResponse) {
      if (result.predictResponse.draw_date) {
        predictDate = result.predictResponse.draw_date;
        // 檢查是否已開獎（有期號表示已開獎）
        isDrawn = !!result.predictResponse.period;
      } else {
        predictDate = '尚未開獎';
      }
    } else {
      predictDate = '尚未開獎';
    }

    const row: (string | number)[] = [result.date, predictDate];

    // 分離一般號碼和特別號
    let normalNumbers: number[] = [];
    let specialNumber: number | undefined;

    if (result.actualNumbers && result.actualNumbers.length > 0 && isDrawn) {
      if (isSuperLotto.value) {
        // 威力彩：所有號碼都是一般號碼
        normalNumbers = [...result.actualNumbers].sort((a, b) => a - b);
      } else {
        // 其他彩種：最後一個是特別號
        if (result.actualNumbers.length > maxNumbers) {
          normalNumbers = result.actualNumbers.slice(0, -1).sort((a, b) => a - b);
          specialNumber = result.actualNumbers[result.actualNumbers.length - 1];
        } else {
          normalNumbers = [...result.actualNumbers].sort((a, b) => a - b);
        }
      }
    }

    // 添加一般號碼
    for (let i = 0; i < maxNumbers; i++) {
      if (i < normalNumbers.length) {
        row.push(normalNumbers[i].toString().padStart(2, '0'));
      } else if (!isDrawn && predictDate !== '尚未開獎') {
        row.push('尚未開獎'); // 如果有預測日期但還沒開獎
      } else {
        row.push(''); // 如果完全沒有數據
      }
    }

    // 添加特別號
    if (hasSpecialNumber) {
      if (specialNumber !== undefined) {
        row.push(specialNumber.toString().padStart(2, '0'));
      } else if (!isDrawn && predictDate !== '尚未開獎') {
        row.push('尚未開獎'); // 如果有預測日期但還沒開獎
      } else {
        row.push(''); // 如果完全沒有數據
      }
    }

    sheetData.push(row);
  });

  const worksheet = XLSX.utils.aoa_to_sheet(sheetData);

  // 設置列寬
  const colWidths = [
    { wch: 12 }, // 分析日期欄
    { wch: 12 }  // 預測日期欄
  ];
  for (let i = 0; i < maxNumbers + (hasSpecialNumber ? 1 : 0); i++) {
    colWidths.push({ wch: 8 }); // 號碼欄
  }
  worksheet['!cols'] = colWidths;

  XLSX.utils.book_append_sheet(workbook, worksheet, '實際開獎號碼');
};



// 創建尾數分析統計工作表（出現次數）
const createTailAppearancesSheet = (workbook: XLSX.WorkBook) => {
  const sheetData: (string | number)[][] = [];

  sheetData.push(['預測尾數統計']);
  sheetData.push([]);
  sheetData.push(['分析日期', '預測日期']);

  // 為每期分析結果創建一行
  batchResults.value.forEach(result => {
    if (result.tailNumAppearances) {
      // 按出現次數排序尾數
      const sortedTails = Array.from(result.tailNumAppearances.entries())
        .sort((a, b) => b[1] - a[1])
        .map(([tail]) => tail);

      // 每10個尾數一列（尾數不使用padding zero）
      const rows = formatNumbersIntoRows(sortedTails, true);
      const predictDate = getPredictDate(result);

      rows.forEach((row, index) => {
        if (index === 0) {
          sheetData.push([result.date, predictDate, ...row]);
        } else {
          sheetData.push(['', '', ...row]); // 空白日期，對齊尾數
        }
      });
    }
  });

  const worksheet = XLSX.utils.aoa_to_sheet(sheetData);

  // 應用紅色字體樣式給與實際開獎尾數相符的預測尾數
  applyRedFontForMatches(worksheet, sheetData, 'tail');

  // 設置列寬
  const colWidths = [
    { wch: 12 }, // 分析日期欄
    { wch: 12 }, // 預測日期欄
    ...Array(10).fill({ wch: 6 }) // 尾數欄
  ];
  worksheet['!cols'] = colWidths;

  XLSX.utils.book_append_sheet(workbook, worksheet, '預測尾數');
};

// 創建版路分析尾數工作表
const createPatternRdTailAnalysisSheet = (workbook: XLSX.WorkBook) => {
  const sheetData: (string | number)[][] = [];

  sheetData.push(['版路分析尾數']);
  sheetData.push([]);
  sheetData.push(['日期']);

  // 為每期分析結果創建一行
  batchResults.value.forEach(result => {
    if (result.rdTailAppearances) {
      // 按出現次數排序尾數
      const sortedTails = Array.from(result.rdTailAppearances.entries())
        .sort((a, b) => b[1] - a[1])
        .map(([tail]) => tail);

      // 每10個尾數一列
      const rows = formatNumbersIntoRows(sortedTails, true);

      rows.forEach((row, index) => {
        if (index === 0) {
          sheetData.push([result.date, ...row]);
        } else {
          sheetData.push(['', ...row]);
        }
      });
    }
  });

  const worksheet = XLSX.utils.aoa_to_sheet(sheetData);

  // 應用★標記給匹配的尾數
  applyTailStarMarks(worksheet, sheetData);

  // 設置列寬
  const colWidths = [
    { wch: 12 }, // 日期欄
    ...Array(10).fill({ wch: 6 }) // 尾數欄
  ];
  worksheet['!cols'] = colWidths;

  XLSX.utils.book_append_sheet(workbook, worksheet, '版路分析尾數');
};

// 創建尾數分析尾數工作表
const createPatternTailRdAnalysisSheet = (workbook: XLSX.WorkBook) => {
  const sheetData: (string | number)[][] = [];

  sheetData.push(['尾數分析尾數']);
  sheetData.push([]);
  sheetData.push(['日期']);

  // 為每期分析結果創建一行
  batchResults.value.forEach(result => {
    if (result.tailRdScoreMap) {
      // 按分數排序尾數
      const sortedTails = Array.from(result.tailRdScoreMap.entries())
        .sort((a, b) => b[1] - a[1])
        .map(([tail]) => tail);

      // 每10個尾數一列
      const rows = formatNumbersIntoRows(sortedTails, true);

      rows.forEach((row, index) => {
        if (index === 0) {
          sheetData.push([result.date, ...row]);
        } else {
          sheetData.push(['', ...row]);
        }
      });
    }
  });

  const worksheet = XLSX.utils.aoa_to_sheet(sheetData);

  // 應用★標記給匹配的尾數
  applyTailStarMarks(worksheet, sheetData);

  // 設置列寬
  const colWidths = [
    { wch: 12 }, // 日期欄
    ...Array(10).fill({ wch: 6 }) // 尾數欄
  ];
  worksheet['!cols'] = colWidths;

  XLSX.utils.book_append_sheet(workbook, worksheet, '尾數分析尾數');
};

// 創建綜合分析尾數工作表
const createPatternComprehensiveAnalysisSheet = (workbook: XLSX.WorkBook) => {
  const sheetData: (string | number)[][] = [];

  sheetData.push(['綜合分析尾數']);
  sheetData.push([]);
  sheetData.push(['日期']);

  // 為每期分析結果創建一行
  batchResults.value.forEach(result => {
    if (result.tailScoreMap) {
      // 按分數排序尾數
      const sortedTails = Array.from(result.tailScoreMap.entries())
        .sort((a, b) => b[1] - a[1])
        .map(([tail]) => tail);

      // 每10個尾數一列
      const rows = formatNumbersIntoRows(sortedTails, true);

      rows.forEach((row, index) => {
        if (index === 0) {
          sheetData.push([result.date, ...row]);
        } else {
          sheetData.push(['', ...row]);
        }
      });
    }
  });

  const worksheet = XLSX.utils.aoa_to_sheet(sheetData);

  // 應用★標記給匹配的尾數
  applyTailStarMarks(worksheet, sheetData);

  // 設置列寬
  const colWidths = [
    { wch: 12 }, // 日期欄
    ...Array(10).fill({ wch: 6 }) // 尾數欄
  ];
  worksheet['!cols'] = colWidths;

  XLSX.utils.book_append_sheet(workbook, worksheet, '綜合分析尾數');
};

// 應用★標記給匹配的尾數（適用於只有一個日期欄位的工作表）
const applyTailStarMarks = (worksheet: XLSX.WorkSheet, sheetData: (string | number)[][]) => {
  // 遍歷數據行，從第4行開始（跳過標題行）
  for (let rowIndex = 3; rowIndex < sheetData.length; rowIndex++) {
    const row = sheetData[rowIndex];
    const dateCell = row[0];

    if (!dateCell || dateCell === '') continue;

    // 找到對應的批量分析結果
    const result = batchResults.value.find(r => r.date === dateCell);
    if (!result || !result.actualNumbers) continue;

    // 獲取實際開獎尾數
    const actualTails = [...new Set(result.actualNumbers.map(n => n % 10))];

    // 檢查每個尾數是否匹配（從第2欄開始，只有一個日期欄位）
    for (let colIndex = 1; colIndex < row.length; colIndex++) {
      const cellValue = row[colIndex];
      if (cellValue && cellValue !== '') {
        const tailNum = parseInt(cellValue.toString(), 10);
        if (actualTails.includes(tailNum)) {
          const markedValue = `${cellValue}★`;
          const cellRef = XLSX.utils.encode_cell({ r: rowIndex, c: colIndex });

          if (!worksheet[cellRef]) {
            worksheet[cellRef] = { v: markedValue, t: 's' };
          } else {
            worksheet[cellRef].v = markedValue;
          }

          // 同時更新原始數據
          sheetData[rowIndex][colIndex] = markedValue;
        }
      }
    }
  }
};

// 應用★標記給匹配的尾數（適用於有兩個日期欄位的工作表）
const applyTailStarMarksWithTwoDates = (worksheet: XLSX.WorkSheet, sheetData: (string | number)[][]) => {
  // 遍歷數據行，從第4行開始（跳過標題行）
  for (let rowIndex = 3; rowIndex < sheetData.length; rowIndex++) {
    const row = sheetData[rowIndex];
    const dateCell = row[0];

    if (!dateCell || dateCell === '') continue;

    // 找到對應的批量分析結果
    const result = batchResults.value.find(r => r.date === dateCell);
    if (!result || !result.actualNumbers) continue;

    // 獲取實際開獎尾數
    const actualTails = [...new Set(result.actualNumbers.map(n => n % 10))];

    // 檢查每個尾數是否匹配（從第3欄開始，跳過分析日期和預測日期）
    for (let colIndex = 2; colIndex < row.length; colIndex++) {
      const cellValue = row[colIndex];
      if (cellValue && cellValue !== '') {
        const tailNum = parseInt(cellValue.toString(), 10);
        if (actualTails.includes(tailNum)) {
          const markedValue = `${cellValue}★`;
          const cellRef = XLSX.utils.encode_cell({ r: rowIndex, c: colIndex });

          if (!worksheet[cellRef]) {
            worksheet[cellRef] = { v: markedValue, t: 's' };
          } else {
            worksheet[cellRef].v = markedValue;
          }

          // 同時更新原始數據
          sheetData[rowIndex][colIndex] = markedValue;
        }
      }
    }
  }
};

// 創建版路+尾數比對結果工作表
const createPatternRdTailComparisonSheet = (workbook: XLSX.WorkBook) => {
  const sheetData: (string | number)[][] = [];

  sheetData.push(['版路+尾數比對結果']);
  sheetData.push([]);
  sheetData.push(['日期']);

  // 為每期分析結果創建一行
  batchResults.value.forEach(result => {
    if (result.tailMatchResults && result.tailMatchResults.length > 0) {
      // 每10個尾數一列
      const rows = formatNumbersIntoRows(result.tailMatchResults, true);

      rows.forEach((row, index) => {
        if (index === 0) {
          sheetData.push([result.date, ...row]);
        } else {
          sheetData.push(['', ...row]);
        }
      });
    } else {
      // 如果沒有比對結果，顯示空行
      sheetData.push([result.date]);
    }
  });

  const worksheet = XLSX.utils.aoa_to_sheet(sheetData);

  // 應用★標記給匹配的尾數
  applyTailStarMarks(worksheet, sheetData);

  // 設置列寬
  const colWidths = [
    { wch: 12 }, // 日期欄
    ...Array(10).fill({ wch: 6 }) // 尾數欄
  ];
  worksheet['!cols'] = colWidths;

  XLSX.utils.book_append_sheet(workbook, worksheet, '版路+尾數比對');
};

// 創建版路+綜合比對結果工作表
const createPatternRdComprehensiveComparisonSheet = (workbook: XLSX.WorkBook) => {
  const sheetData: (string | number)[][] = [];

  sheetData.push(['版路+綜合比對結果']);
  sheetData.push([]);
  sheetData.push(['日期']);

  // 為每期分析結果創建一行
  batchResults.value.forEach(result => {
    if (result.tailMatchResults2 && result.tailMatchResults2.length > 0) {
      // 每10個尾數一列
      const rows = formatNumbersIntoRows(result.tailMatchResults2, true);

      rows.forEach((row, index) => {
        if (index === 0) {
          sheetData.push([result.date, ...row]);
        } else {
          sheetData.push(['', ...row]);
        }
      });
    } else {
      // 如果沒有比對結果，顯示空行
      sheetData.push([result.date]);
    }
  });

  const worksheet = XLSX.utils.aoa_to_sheet(sheetData);

  // 應用★標記給匹配的尾數
  applyTailStarMarks(worksheet, sheetData);

  // 設置列寬
  const colWidths = [
    { wch: 12 }, // 日期欄
    ...Array(10).fill({ wch: 6 }) // 尾數欄
  ];
  worksheet['!cols'] = colWidths;

  XLSX.utils.book_append_sheet(workbook, worksheet, '版路+綜合比對');
};

// 創建尾數+綜合比對結果工作表
const createPatternTailComprehensiveComparisonSheet = (workbook: XLSX.WorkBook) => {
  const sheetData: (string | number)[][] = [];

  sheetData.push(['尾數+綜合比對結果']);
  sheetData.push([]);
  sheetData.push(['日期']);

  // 為每期分析結果創建一行
  batchResults.value.forEach(result => {
    if (result.tailMatchResults3 && result.tailMatchResults3.length > 0) {
      // 每10個尾數一列
      const rows = formatNumbersIntoRows(result.tailMatchResults3, true);

      rows.forEach((row, index) => {
        if (index === 0) {
          sheetData.push([result.date, ...row]);
        } else {
          sheetData.push(['', ...row]);
        }
      });
    } else {
      // 如果沒有比對結果，顯示空行
      sheetData.push([result.date]);
    }
  });

  const worksheet = XLSX.utils.aoa_to_sheet(sheetData);

  // 應用★標記給匹配的尾數
  applyTailStarMarks(worksheet, sheetData);

  // 設置列寬
  const colWidths = [
    { wch: 12 }, // 日期欄
    ...Array(10).fill({ wch: 6 }) // 尾數欄
  ];
  worksheet['!cols'] = colWidths;

  XLSX.utils.book_append_sheet(workbook, worksheet, '尾數+綜合比對');
};

// 創建帶標記的版路分析預測號碼工作表
const createPatternPredictNumbersSheet = (workbook: XLSX.WorkBook) => {
  const sheetData: (string | number)[][] = [];

  sheetData.push(['版路分析-預測號碼統計']);
  sheetData.push([]);
  sheetData.push(['分析日期', '預測日期']);

  // 為每期分析結果創建一行
  batchResults.value.forEach(result => {
    if (result.targetNumAppearances) {
      // 按出現次數排序預測號碼
      const sortedNumbers = Array.from(result.targetNumAppearances.entries())
        .sort((a, b) => b[1] - a[1])
        .map(([num]) => num);

      // 每10個號碼一列
      const rows = formatNumbersIntoRows(sortedNumbers);
      const predictDate = getPredictDate(result);

      rows.forEach((row, index) => {
        if (index === 0) {
          sheetData.push([result.date, predictDate, ...row]);
        } else {
          sheetData.push(['', '', ...row]); // 空白日期，對齊號碼
        }
      });
    }
  });

  const worksheet = XLSX.utils.aoa_to_sheet(sheetData);

  // 應用紅色字體樣式給與實際開獎號碼相符的預測號碼
  applyRedFontForMatches(worksheet, sheetData, 'predict');

  // 設置列寬
  const colWidths = [
    { wch: 12 }, // 分析日期欄
    { wch: 12 }, // 預測日期欄
    ...Array(10).fill({ wch: 8 }) // 號碼欄
  ];
  worksheet['!cols'] = colWidths;

  XLSX.utils.book_append_sheet(workbook, worksheet, '版路分析-預測號碼');
};

// 創建帶標記的版路分析未出現號碼工作表 - 依預測次數排序
const createPatternNonAppearedByFrequencySheet = (workbook: XLSX.WorkBook) => {
  const sheetData: (string | number)[][] = [];

  sheetData.push(['版路分析-未出現號碼 - 依預測次數排序']);
  sheetData.push([]);
  sheetData.push(['分析日期', '預測日期']);

  batchResults.value.forEach(result => {
    if (result.nonAppearedByFrequency) {
      // 依預測次數排序
      const sortedByFreq = Array.from(result.nonAppearedByFrequency.entries())
        .sort((a, b) => b[1] - a[1])
        .map(([num]) => num);

      // 每10個號碼一列
      const rows = formatNumbersIntoRows(sortedByFreq);
      const predictDate = getPredictDate(result);

      rows.forEach((row, index) => {
        if (index === 0) {
          sheetData.push([result.date, predictDate, ...row]);
        } else {
          sheetData.push(['', '', ...row]);
        }
      });
    }
  });

  const worksheet = XLSX.utils.aoa_to_sheet(sheetData);

  // 應用紅色字體樣式（理論上未出現號碼不會有匹配，但為了完整性添加）
  applyRedFontForMatches(worksheet, sheetData, 'predict');

  // 設置列寬
  const colWidths = [
    { wch: 12 }, // 分析日期欄
    { wch: 12 }, // 預測日期欄
    ...Array(10).fill({ wch: 8 }) // 號碼欄
  ];
  worksheet['!cols'] = colWidths;

  XLSX.utils.book_append_sheet(workbook, worksheet, '版路分析-未出現號碼-預測次數');
};

// 創建帶標記的版路分析未出現號碼工作表 - 依大小排序
const createPatternNonAppearedBySizeSheet = (workbook: XLSX.WorkBook) => {
  const sheetData: (string | number)[][] = [];

  sheetData.push(['版路分析-未出現號碼 - 依大小排序']);
  sheetData.push([]);
  sheetData.push(['分析日期', '預測日期']);

  batchResults.value.forEach(result => {
    if (result.nonAppearedNumbers) {
      // 依大小排序
      const sortedBySize = [...result.nonAppearedNumbers].sort((a, b) => a - b);

      // 每10個號碼一列
      const rows = formatNumbersIntoRows(sortedBySize);
      const predictDate = getPredictDate(result);

      rows.forEach((row, index) => {
        if (index === 0) {
          sheetData.push([result.date, predictDate, ...row]);
        } else {
          sheetData.push(['', '', ...row]);
        }
      });
    }
  });

  const worksheet = XLSX.utils.aoa_to_sheet(sheetData);

  // 應用紅色字體樣式
  applyRedFontForMatches(worksheet, sheetData, 'predict');

  // 設置列寬
  const colWidths = [
    { wch: 12 }, // 分析日期欄
    { wch: 12 }, // 預測日期欄
    ...Array(10).fill({ wch: 8 }) // 號碼欄
  ];
  worksheet['!cols'] = colWidths;

  XLSX.utils.book_append_sheet(workbook, worksheet, '版路分析-未出現號碼-大小排序');
};

// 創建帶標記的版路分析尾數統計工作表
const createPatternTailNumbersSheet = (workbook: XLSX.WorkBook) => {
  const sheetData: (string | number)[][] = [];

  sheetData.push(['版路分析-尾數統計']);
  sheetData.push([]);
  sheetData.push(['分析日期', '預測日期']);

  batchResults.value.forEach(result => {
    if (result.tailNumAppearances) {
      // 按出現次數排序尾數
      const sortedTails = Array.from(result.tailNumAppearances.entries())
        .sort((a, b) => b[1] - a[1])
        .map(([tail]) => tail);

      // 每10個尾數一列
      const rows = formatNumbersIntoRows(sortedTails, true);
      const predictDate = getPredictDate(result);

      rows.forEach((row, index) => {
        if (index === 0) {
          sheetData.push([result.date, predictDate, ...row]);
        } else {
          sheetData.push(['', '', ...row]);
        }
      });
    }
  });

  const worksheet = XLSX.utils.aoa_to_sheet(sheetData);

  // 應用★標記給匹配的尾數
  applyTailStarMarksWithTwoDates(worksheet, sheetData);

  // 設置列寬
  const colWidths = [
    { wch: 12 }, // 分析日期欄
    { wch: 12 }, // 預測日期欄
    ...Array(10).fill({ wch: 6 }) // 尾數欄
  ];
  worksheet['!cols'] = colWidths;

  XLSX.utils.book_append_sheet(workbook, worksheet, '版路分析-尾數統計');
};

// 創建帶標記的尾數分析預測尾數工作表
const createPatternTailAppearancesSheet = (workbook: XLSX.WorkBook) => {
  const sheetData: (string | number)[][] = [];

  sheetData.push(['尾數分析-預測尾數統計']);
  sheetData.push([]);
  sheetData.push(['分析日期', '預測日期']);

  // 為每期分析結果創建一行
  batchResults.value.forEach(result => {
    if (result.tailAnalysisAppearances) {
      // 按出現次數排序尾數（使用尾數分析的專用統計數據）
      const sortedTails = Array.from(result.tailAnalysisAppearances.entries())
        .sort((a, b) => b[1] - a[1])
        .map(([tail]) => tail);

      // 每10個尾數一列
      const rows = formatNumbersIntoRows(sortedTails, true);
      const predictDate = getPredictDate(result);

      rows.forEach((row, index) => {
        if (index === 0) {
          sheetData.push([result.date, predictDate, ...row]);
        } else {
          sheetData.push(['', '', ...row]);
        }
      });
    }
  });

  const worksheet = XLSX.utils.aoa_to_sheet(sheetData);

  // 應用★標記給匹配的尾數
  applyTailStarMarksWithTwoDates(worksheet, sheetData);

  // 設置列寬
  const colWidths = [
    { wch: 12 }, // 分析日期欄
    { wch: 12 }, // 預測日期欄
    ...Array(10).fill({ wch: 6 }) // 尾數欄
  ];
  worksheet['!cols'] = colWidths;

  XLSX.utils.book_append_sheet(workbook, worksheet, '尾數分析-預測尾數');
};



// 創建綜合分析工作表
const createPatternSheet = (workbook: XLSX.WorkBook) => {
  // 創建綜合分析專用工作表（放在最前面）
  createPatternRdTailAnalysisSheet(workbook);
  createPatternTailRdAnalysisSheet(workbook);
  createPatternComprehensiveAnalysisSheet(workbook);
  createPatternRdTailComparisonSheet(workbook);
  createPatternRdComprehensiveComparisonSheet(workbook);
  createPatternTailComprehensiveComparisonSheet(workbook);

  // 創建版路分析子表（添加標記）
  createPatternPredictNumbersSheet(workbook);
  createPatternNonAppearedByFrequencySheet(workbook);
  createPatternNonAppearedBySizeSheet(workbook);
  createPatternTailNumbersSheet(workbook);

  // 創建尾數分析子表（添加標記）
  createPatternTailAppearancesSheet(workbook);

  // 創建實際開獎號碼工作表
  createActualNumbersSheet(workbook);
};

// 初始化
onMounted(() => {
  // 設定預設參考日期為今天
  referenceDate.value = new Date().toISOString().split('T')[0];
});
</script>

<style lang="scss" scoped>
.q-card {
  max-width: 1200px;
  margin: 0 auto;
}

.q-option-group {
  .q-radio {
    margin-right: 2rem;
  }
}
</style>
